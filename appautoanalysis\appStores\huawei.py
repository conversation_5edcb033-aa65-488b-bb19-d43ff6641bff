#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
from base.mysql_helper import MysqlHelper
from base.crawl_tools import html_content


'''
create table huawei_app_store(
app_name varchar(100) not null,
version varchar(50) not null,
size int,
package_name varchar(150),
down_count varchar(20),
type varchar(10) not null,
sub_type varchar(10) not null,
apk_md5 varchar(50) not null,
down_url varchar(1000),
icon_url varchar(1000),
primary key(app_name, package_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
'''

uriid_url = 'https://web-drcn.hispace.dbankcloud.cn/uowap/index?method=internal.getTemplate&serviceType=20&zone=&locale=zh_CN'
url = 'https://web-drcn.hispace.dbankcloud.cn/uowap/index?method=internal.getTabDetail&serviceType=20&zone=&locale=zh_CN&uri={}&reqPageNum={}'
download_url = 'https://appgallery.huawei.com/#/app/{}'

referer = 'https://appgallery.huawei.com/'


# get uriid
def get_uriid(response):
    uriids = dict()
    if response:
        if type(response) == tuple:
            response = response[0]
        tmp_data = json.loads(response.decode('utf-8'))
        tabinfo = tmp_data.get('tabInfo')
        for i in tabinfo:
            tab_name = i.get('tabName')
            tab_id = i.get('tabId')
            tab_info = i.get('tabInfo')
            if tab_info:
                for j in tab_info:
                    if uriids.get(tab_name):
                        uriids[tab_name].append((j.get('tabName'), j.get('tabId')))
                    else:
                        uriids[tab_name] = [(j.get('tabName'), j.get('tabId'))]
    return uriids


# get tags uriid
def get_sub_uriid(response):
    sub_uriid = dict()
    if response:
        data = json.loads(response.decode('utf-8'))
        tab_info = data.get('tabInfo')
        if tab_info:
            for i in tab_info:
                sub_uriid[i.get('tabName')] = i.get('tabId')
    return sub_uriid


# get app info
def get_apps(response):
    all_apps = list()
    if response:
        if type(response) == tuple:
            response = response[0]
        data = json.loads(response.decode('utf-8'))
        layout_data = data.get('layoutData')
        if layout_data:
            for _apps in layout_data:
                apps = _apps.get('dataList')
                for app in apps:
                    app_info = dict()
                    app_info['app_name'] = app.get('name')
                    app_info['md5'] = app.get('md5')
                    down_url = app.get('downloadRecommendUri').split('|')[1]
                    app_info['down_url'] = download_url.format(down_url)
                    app_info['size'] = app.get('size')
                    app_info['package_name'] = app.get('package')
                    app_info['app_version'] = app.get('appVersionName')
                    app_info['down_count_desc'] = app.get('downCountDesc')
                    app_info['icon_url'] = app.get('icon')
                    app_info['app_sub_type'] = app.get('tagName')
                    all_apps.append(app_info)
    return all_apps


def save(data):
    _name = data[0]
    _pkg_name = data[3]
    s_sql = '''select concat(app_name, "_", package_name), version from huawei_app_store where app_name = "%s" and package_name = "%s";''' % (_name, _pkg_name)
    d_sql = '''delete from huawei_app_store where app_name = "%s" and package_name = "%s";''' % (_name, _pkg_name)
    i_sql = 'insert into huawei_app_store (app_name,version,size,package_name,down_count,type,sub_type,apk_md5,down_url,icon_url) values ("%s", "%s", "%s", "%s", "%s", "%s", "%s", "%s", "%s", "%s")'
    mysql = MysqlHelper('apps')
    result = mysql.select(s_sql)
    if result:
        print(result)
        app_name, version = result[0]
        if _name + "_" + _pkg_name == app_name and version != data[1]:
            mysql.delete(d_sql)
            mysql.insert(i_sql, data)
    else:
        mysql.insert(i_sql, data)
    mysql.db_close()


def to_number(str_cnt):
    if str_cnt.startswith('<'):
        return 9999
    num, suffix = str_cnt.split(' ')
    if ',' in num:
        num = int(num.replace(',', ''))
    else:
        num = int(num)
    suffix = suffix[0]
    if suffix == '万':
        num = num * 10000
    elif suffix == '亿':
        num = num * 100000000

    return num


def main():
    # 获取分类信息
    uriid_resp = html_content(uriid_url, refer=referer)
    if uriid_resp:
        uriids = get_uriid(uriid_resp)
        if uriids:
            for name, value in uriids.items():
                # 对应用，游戏类下对分类进行app爬取
                if name in ['应用', '游戏']:
                    for sub_name, sub_id in value:
                        page = 1
                        exit_flag = 0
                        while True:
                            app_data_resp = html_content(url.format(sub_id, page), refer=referer)
                            if app_data_resp:
                                data = get_apps(app_data_resp)
                                if not data and exit_flag < 5:
                                    exit_flag += 1
                                    continue
                                elif not data and exit_flag == 5:
                                    break
                                else:
                                    page += 1
                                    for i in data:
                                        i['app_type'] = sub_name
                                        _ = (i.get('app_name'), i.get('app_version'), i.get('size'),
                                                i.get('package_name'), to_number(i.get('down_count_desc')),
                                                i.get('app_type'), i.get('app_sub_type'), i.get('md5'),
                                                i.get('down_url'), i.get('icon_url'))
                                        save(_)


if __name__ == '__main__':
    main()

# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   extract_from_pcap.py
@Time    :   2023/8/31 10:13
"""
import os
import csv
import datetime
import asyncio
import pyshark
import platform
import time
from apscheduler.schedulers.background import BackgroundScheduler
import threading
from multiprocessing import Queue, connection, Process, Pipe

SYSTEM = platform.system()
TSHARK = "/usr/bin/tshark" if SYSTEM == "Linux" else "D:\\Program Files\\Wireshark\\tshark.exe"
RESULT_DIR = "/home/<USER>" if SYSTEM == "Linux" else "D:\\log"

PROCESSES = 1

HOSTS = ["**************:8081", "************:81", "************01:81",
         "**************:3306", "**************", "**************", "************:8080"]
PCAP_DIR = "/home/<USER>/pcap" if SYSTEM == "Linux" else r"D:\data\拨测\20230908"


def extract(pcap: str) -> list:
    loop = asyncio.ProactorEventLoop()
    asyncio.set_event_loop(loop)
    packets = pyshark.FileCapture(pcap, tshark_path=TSHARK, display_filter='http', eventloop=loop)
    packets.load_packets()
    samples = list()
    try:
        for packet in packets:
            highest_layer = packet.highest_layer
            if highest_layer == "JSON" and "HTTP" in packet:
                highest_layer = "HTTP"
            if highest_layer == "HTTP":
                print("success")
                if hasattr(packet[highest_layer], 'host'):
                    host = str(packet[highest_layer].host)
                    if host == "************:81":
                        print("success")
                    if host in HOSTS:
                        sample = {"url": "", "user_agent": "", "cookie": "", "data": ""}
                        if hasattr(packet[highest_layer], 'request_full_uri'):
                            uri = str(packet[highest_layer].request_full_uri)
                            sample["url"] = uri
                        if hasattr(packet[highest_layer], 'user_agent'):
                            ua = str(packet[highest_layer].user_agent)
                            sample["user_agent"] = ua
                        if hasattr(packet[highest_layer], 'cookie'):
                            cookie = str(packet[highest_layer].cookie)
                            sample["cookie"] = cookie
                        print("data", packet[highest_layer].get_field("data"))
                        samples.append(sample)
    except Exception as e:
        print(e)
    finally:
        packets.close()
    return samples


def sample_from_pcap(pcap: str):
    loop = asyncio.ProactorEventLoop()
    asyncio.set_event_loop(loop)
    packets = pyshark.FileCapture(pcap, tshark_path=TSHARK, display_filter='http', eventloop=loop)
    # packets.load_packets()
    start = time.time()
    try:
        for packet in packets:
            highest_layer = packet.highest_layer
            if hasattr(packet[highest_layer], 'request_full_uri'):
                uri = str(packet[highest_layer].request_full_uri)
                print(uri)
    except Exception as e:
        print(e)
    finally:
        packets.close()
    print(f'cost time: {int(time.time() - start)}')


def extract_pcaps():
    pcap_dir = r"E:\项目\江苏物联网\8期\拨测\pcap"
    file_list = os.listdir(pcap_dir)
    for file_name in file_list:
        pcap_path = os.path.join(pcap_dir, file_name)
        sample_from_pcap(pcap_path)


def collect_feature(target_dir: str):
    dirs = os.listdir(target_dir)
    count = len(dirs)
    samples = []
    for index, pcap in enumerate(dirs):
        print(f"analysing {pcap}...{index+1}/{count}")
        pcap_path = os.path.join(target_dir, pcap)
        each_sample = extract(pcap_path)
        samples += each_sample
    write_result(samples)


def write_result(samples: list):
    result_excel = f"analysis_result-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}.csv"
    path = os.path.join(RESULT_DIR, result_excel)
    header = ["url", "user_agent", "cookie", "data"]
    if not os.path.exists(path):
        with open(path, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
    with open(path, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in samples:
            writer.writerow([row["url"], row["user_agent"], row["cookie"], row["data"]])


class Analysis(object):
    def __init__(self, queue: Queue, connect: connection.Connection):
        self.queue = queue
        self.connect = connect
        self.done = True
        self.samples = []

    def run(self):
        while True:
            self.done = True
            item = self.queue.get()
            self.done = False
            self.extract(item)

    def extract(self, pcap: str):
        if SYSTEM == "Linux":
            loop = None
        else:
            loop = asyncio.ProactorEventLoop()
            asyncio.set_event_loop(loop)
        packets = pyshark.FileCapture(pcap, tshark_path=TSHARK, display_filter='http', eventloop=loop)
        packets.load_packets()
        try:
            for packet in packets:
                highest_layer = packet.highest_layer
                if hasattr(packet[highest_layer], 'host') and hasattr(packet[highest_layer], 'request_full_uri'):
                    host = str(packet[highest_layer].host)
                    if host in HOSTS:
                        uri = str(packet[highest_layer].request_full_uri)
                        sample = uri.split("/")[-1]
                        if sample not in self.samples:
                            self.samples.append(sample)
        except Exception as e:
            print(e)
        finally:
            packets.close()

    def recv_signal(self):
        while True:
            command = self.connect.recv()
            if command == 0x9999:
                if self.done:
                    self.connect.send(1)
                else:
                    self.connect.send(0)
            if command == 0x1111:
                self.connect.send(self.samples)


def consumer(queue: Queue, connect: connection.Connection):
    analysis = Analysis(queue=queue, connect=connect)
    threading.Thread(target=analysis.recv_signal).start()
    # analysis.run()
    threading.Thread(target=analysis.run).start()


class SubProcess(object):
    """消费子进程，通过管道与主进程通信"""
    def __init__(self, process: Process, connect: connection.Connection):
        """
        :param process: 消费进程
        :param connect: pipe管道
        """
        self.process = process
        self.connect = connect


class Manager(object):
    def __init__(self):
        self.processes = list()
        self.queue = Queue(10)
        self.samples = list()
        self.done = False

    def __create_processes(self):
        for i in range(PROCESSES):
            conn_1, conn_2 = Pipe()
            sub_process = Process(target=consumer, args=(self.queue, conn_1))
            self.processes.append(SubProcess(process=sub_process, connect=conn_2))

    def start(self):
        dirs = os.listdir(PCAP_DIR)
        count = len(dirs)
        for index, pcap in enumerate(dirs):
            pcap_path = os.path.join(PCAP_DIR, pcap)
            self.queue.put(pcap_path)
        self.__create_processes()
        for i, p in enumerate(self.processes):
            p.process.start()
        scan = BackgroundScheduler()
        scan.add_job(self.result, "interval", seconds=10)
        scan.start()
        while True:
            time.sleep(10)

    def result(self):
        cmd_done = 0x9999
        cmd_result = 0x1111
        for index, sub_pro in enumerate(self.processes):
            sub_pro.connect.send(cmd_done)
            stat = sub_pro.connect.recv()
            if stat == 0:
                print("wait a moment...")
                return
        for index, sub_pro in enumerate(self.processes):
            sub_pro.connect.send(cmd_result)
            samples = sub_pro.connect.recv()
            self.samples += samples
        self.samples = list(set(self.samples))
        if not self.done:
            self.write_result()
        self.done = True
        print("analysis over...")

    def write_result(self):
        result_excel = f"analysis_result-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}.csv"
        path = os.path.join(RESULT_DIR, result_excel)
        header = ["MD5"]
        if not os.path.exists(path):
            with open(path, 'w', encoding="UTF-8-sig", newline='') as f:
                writer = csv.writer(f)
                writer.writerow(header)
        with open(path, 'a+', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            for row in self.samples:
                writer.writerow([row])


def modify():
    path = r"D:\data\拨测\20230913\dat\864452066331115.txt"
    path2 = r"D:\data\拨测\20230913\dat\864452066331115_mdf.txt"
    with open(path, 'r', 'UTF-8') as f:
        rows = f.readlines()
    data = []
    for row in rows:
        print(row)
    with open(path2, 'a+') as f2:
        for row in rows:
            f2.write(row)




if __name__ == "__main__":
    # path = r"D:\data\拨测\20230830\拨测2023083014\拨测2023083014\1693305288_02_6_BRD_6c82775e26bcbd95_20230830145534707_20230830145534707_00000001\test"
    # host = "mobilesecurity.kittycat520.top:7081"
    # collect_feature(target_dir=PCAP_DIR)
    # manager = Manager()
    # manager.start()
    # modify()
    extract_pcaps()


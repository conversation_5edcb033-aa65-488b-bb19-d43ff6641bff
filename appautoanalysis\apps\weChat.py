#!/usr/bin/env python
# -*- coding: utf-8 -*-


import sys
import os
sys.path.append('../')
from base.desired_caps import desired_cap, load_config, quite_driver
from base.adb_tools import AdbTools
from base.webdriver_tools import WebdriverTools
import time
from base.log import Logger
import random


logger = Logger()


class WeChat(object):

    def __init__(self):
        # 数字按键位置
        self.num_postions = {1: (172, 1583), 2: (532, 1583), 3: (880, 1583), 4: (172, 1735), 5: (532, 1735),
                             6: (880, 1735), 7: (172, 1893), 8: (532, 1893), 9: (880, 1893)}
        conf = load_config()
        self.app_package = conf['apps']['weChat']['appPackage']
        self.app_activity = conf['apps']['weChat']['appActivity']
        try:
            self.meid = os.popen('adb devices').readlines()[1].split('\t')[0]
        except:
            logger.warning('meid not found!!!')
            self.meid = ''

    def new_appium_session(self):
        self.driver = desired_cap(self.app_package, self.app_activity)
        self.adb_tools = AdbTools()
        self.webdriver_tools = WebdriverTools(self.driver)
        self.adb_tools.check_display_power_state('9527')

    @staticmethod
    def login(self, user=None, passwd=None):
        id_nfc = 'android:id/button1'
        id_login = 'com.tencent.mm:id/fam'
        id_login_with_qq = 'com.tencent.mm:id/d5u'
        text_user = '请填写微信号/QQ号/邮箱'
        text_passwd = '请填写密码'
        id_login_button = 'com.tencent.mm:id/d5n'
        id_ = 'com.tencent.mm:id/doz'
        text_rule = '允许'  # 媒体/通讯 两次
        # 滑动
        text_agree = '我已阅读并同意上述条款'
        text_agree = '下一步'
        text_yanzheng = '开始验证'
        text_message = '短信验证'
        text_msg = '立即发送短信'
        id_msg = 'com.android.mms:id/embedded_text_editor'
        text_yanzheng = '完成'
        id_f = 'com.tencent.mm:id/dom'

    def choose_friend(self, friend):
        # 查找朋友
        _f_xpath = ('xpath', '//android.view.View[@text="%s"]' % friend)
        # 搜索键
        search_button = [(864, 109), (929, 174)]
        # 搜索文本输入框
        text_box = ('id', 'com.tencent.mm:id/bhn')
        # 选择朋友
        choose_button = ('id', 'com.tencent.mm:id/gbv')

        time.sleep(5)
        if self.webdriver_tools.is_element_exists(friend):
            f_xpath = self.webdriver_tools.find_element(_f_xpath)
            self.webdriver_tools.click(f_xpath)
        else:
            self.webdriver_tools.tap_postion(search_button)
            input_box = self.webdriver_tools.find_element(text_box)
            self.webdriver_tools.send_keys(input_box, friend)
            if not self.webdriver_tools.is_element_exists(friend):
                logger.error('[%s] not found, is he/she in your address?' % friend)
                exit(-1)
            else:
                choose_btn = self.webdriver_tools.find_element(choose_button)
                self.webdriver_tools.click(choose_btn)

    def back_to_main_page(self):
        '''
        回到微信首页
        :return:
        '''
        cnt = 0
        time.sleep(1)
        while True:
            if cnt > 3:
                break
            time.sleep(2)
            # if self.webdriver_tools.is_element_exists('华西第二医院'):
            if self.webdriver_tools.is_element_exists('发现'):
                    break
            else:
                self.driver.back()
                cnt += 1
        logger.info('finished! back to mainpage of app.')

    def choose_more_func(self):
        '''
        消息发送界面，选择更多发送选项
        :return:
        '''
        time.sleep(4)
        _more_func = ('id', 'com.tencent.mm:id/aks')
        times = 0
        while not self.webdriver_tools.is_element_exists('相册'):
            try:
                more_func = self.webdriver_tools.find_element(_more_func)
                self.webdriver_tools.click(more_func)
            except:
                if times > 5:
                    break
            times += 1

    def send_text_msg(self, friend, msg):
        '''
        文本消息
        :param friend:
        :param msg:
        :return:
        '''
        # 聊天界面文本输入框
        _input_btn = ('id', 'com.tencent.mm:id/iy0')
        # 发送按键
        _send_btn = ('id', 'com.tencent.mm:id/anv')
        capture_timeout = 5

        logger.info('send [text] message to [%s]' % friend)
        self.adb_tools.choose_ime()
        self.adb_tools.capture(self.meid, capture_timeout)
        input_btn = self.webdriver_tools.find_element(_input_btn)
        self.webdriver_tools.send_keys(input_btn, msg)
        send_btn = self.webdriver_tools.find_element(_send_btn)
        self.webdriver_tools.click(send_btn)
        self.adb_tools.end_capture(self.meid, '微信文本消息', capture_timeout)

    def send_pic_msg(self, friend):
        '''
        图片消息
        :param friend:
        :return:
        '''
        _pic_btn_postion = [(91, 1497), (242, 1648)]
        _pic_postion = [(0, 209), (266, 475)]
        _send_btn = ('id', 'com.tencent.mm:id/ch')
        capture_timeout = 8

        logger.info('send [picture] to [%s]' % friend)
        self.adb_tools.capture(self.meid, capture_timeout)
        self.choose_more_func()
        self.webdriver_tools.tap_postion(_pic_btn_postion)
        self.webdriver_tools.tap_postion(_pic_postion)
        send_btn = self.webdriver_tools.find_element(_send_btn)
        self.webdriver_tools.click(send_btn)
        time.sleep(5)
        self.adb_tools.end_capture(self.meid, '微信发送图片', capture_timeout)

    def send_red_packet(self, friend, msg, password):
        '''
        红包
        :param friend:
        :param msg:
        :param password:
        :return:
        '''
        _red_packet_postion = [(91, 1795), (242, 1946)]
        _money_box = ('id', 'com.tencent.mm:id/dbc')
        _msg_box = ('id', 'com.tencent.mm:id/dez')
        _send_btn = ('id', 'com.tencent.mm:id/ddo')
        _confirm_btn = ('id', 'com.tencent.mm:id/i7p')
        cancel = [(156, 815), (210, 869)]
        money = '0.01'
        capture_timeout = 25

        logger.info('send [red packet] to [%s]' % friend)
        self.adb_tools.capture(self.meid, capture_timeout)
        t = time.time()
        self.choose_more_func()
        self.webdriver_tools.tap_postion(_red_packet_postion)
        money_box = self.webdriver_tools.find_element(_money_box)
        msg_box = self.webdriver_tools.find_element(_msg_box)
        send_btn = self.webdriver_tools.find_element(_send_btn)
        self.webdriver_tools.send_keys(money_box, money)
        self.webdriver_tools.send_keys(msg_box, msg)
        self.webdriver_tools.click(send_btn)
        if self.webdriver_tools.is_element_exists('确认发送'):
            confirm_btn = self.webdriver_tools.find_element(_confirm_btn)
            self.webdriver_tools.click(confirm_btn)
            time.sleep(2)
        if self.webdriver_tools.is_element_exists('请验证指纹'):
            self.webdriver_tools.tap_postion(cancel)
        # 输入密码
        time.sleep(5)
        try:
            for i in password:
                self.webdriver_tools.tap_postion([self.num_postions.get(int(i))], 1)
                time.sleep(0.1)
        except Exception as e:
            logger.error(e)
        time.sleep(4)
        if not self.webdriver_tools.is_element_exists('微信红包'):
            logger.error('red packet send to [%s] failed!!!' % friend)
            self.adb_tools.end_capture_without_save(self.meid)
            # exit(-1)
        else:
            end_time = time.time() - t
            end_time = capture_timeout - int(end_time)
            self.adb_tools.end_capture(self.meid, '微信发送红包', end_time)

    def send_video_msg(self, friend, _type=1):
        '''
        视频和语音通话
        :param friend:
        :param _type: 1 视频， 2 语音
        :return:
        '''
        video_btn_postion = [(587, 1497), (738, 1648)]
        _end_call_postion = [(544, 1896)]
        chose_one = ('id', 'com.tencent.mm:id/gam')
        _yes_btn = ('id', 'com.tencent.mm:id/doz')
        wait_time = random.randint(10, 30)
        capture_timeout = wait_time + 5

        if _type == 1:
            search_txt = '视频通话'
        elif _type == 2:
            search_txt = '语音通话'
        logger.info('send [%s] to [%s]' % (search_txt, friend))
        self.adb_tools.capture(self.meid, capture_timeout)
        self.choose_more_func()
        self.webdriver_tools.tap_postion(video_btn_postion)
        eles = self.webdriver_tools.find_elements(chose_one)
        eles[_type - 1].click()
        if self.webdriver_tools.is_element_exists('在移动网络环境下会影响视频和音频质量'):
            yes_btn = self.webdriver_tools.find_element(_yes_btn)
            self.webdriver_tools.click(yes_btn)
        time.sleep(wait_time)
        self.webdriver_tools.tap_postion(_end_call_postion)
        self.adb_tools.end_capture(self.meid, '微信{}'.format(search_txt), capture_timeout)

    def send_msg_with_voice(self, friend, msg_duration):
        '''
        语音消息
        :param friend:
        :param msg_duration: 毫秒，语音消息时长
        :return:
        '''
        _voice_btn = ('id', 'com.tencent.mm:id/anc')
        _press_btn = ('id', 'com.tencent.mm:id/grk')
        capture_timeout = msg_duration + 5

        logger.info('send message to [%s] with [voice]' % friend)
        self.adb_tools.capture(self.meid, capture_timeout)
        r_flag = self.webdriver_tools.is_element_exists('按住 说话')
        if not r_flag:
            voice_btn = self.webdriver_tools.find_element(_voice_btn)
            self.webdriver_tools.click(voice_btn)
        press_btn = self.webdriver_tools.find_element(_press_btn)
        self.webdriver_tools.l_press(press_btn, msg_duration=msg_duration)
        self.adb_tools.end_capture(self.meid, '微信发送语音消息', capture_timeout)

    def send_emoticon(self, friend, msg=None):
        '''
        发送表情，msg不为None时，发送文本 + 表情
        :param friend:
        :param msg:
        :return:
        '''
        _msg_btn = ('id', 'com.tencent.mm:id/iy0')
        _emo_btns = ('xpath', '//android.support.v7.widget.RecyclerView[@resource-id="com.tencent.mm:id/fjx"]//'
                              'android.widget.ImageView')
        _emo_btn = ('id', 'com.tencent.mm:id/anz')
        _emos = ('xpath', '//com.tencent.mm.ui.MMImageView')
        _send_btn = ('id', 'com.tencent.mm:id/anv')
        capture_timeout = 10

        logger.info('send [emoticon] message to [%s]' % friend)
        self.adb_tools.capture(self.meid, capture_timeout)
        # 文本消息
        if msg:
            self.adb_tools.choose_ime()
            msg_btn = self.webdriver_tools.find_element(_msg_btn)
            self.webdriver_tools.send_keys(msg_btn, msg)
        # 表情
        emo_btn = self.webdriver_tools.find_element(_emo_btn)
        self.webdriver_tools.click(emo_btn)
        # 随机第一屏表情
        emo_btns = self.webdriver_tools.find_elements(_emo_btns)
        if emo_btns:
            # 去掉搜索表情和拍照表情
            emo_btns.pop(3)
            emo_btns.pop(0)
        self.webdriver_tools.click(emo_btns[random.randint(0, len(emo_btns) - 1)])
        time.sleep(1)
        emos = self.webdriver_tools.find_elements(_emos)
        self.webdriver_tools.click(emos[random.randint(1, len(emos) - 1)])
        if self.webdriver_tools.is_element_exists('发送'):
            send_btn = self.webdriver_tools.find_element(_send_btn)
            self.webdriver_tools.click(send_btn)
        self.adb_tools.end_capture(self.meid, '微信发送表情', capture_timeout)

    def moments(self):
        '''
        浏览朋友圈,滑动n次；带随机点赞/取消点赞
        :return:
        '''
        _lab_btns = ('xpath', '//android.widget.RelativeLayout[@resource-id="com.tencent.mm:id/czl"]'
                              '//android.widget.ImageView')
        _sa = ('xpath', '//android.widget.ImageView[@content-desc="评论"]')
        _btn = ('id', 'com.tencent.mm:id/i8')
        _loc = ('id', 'android:id/title')
        _other_frind_lab = ('id', 'com.tencent.mm:id/e3x')
        _in_frind_page = ('id', 'com.tencent.mm:id/ja')
        _frind_more_moment = ('id', 'com.tencent.mm:id/b__')
        _in_thumb = ('id', 'com.tencent.mm:id/d1r')
        _in_comment = ('id', 'com.tencent.mm:id/axk')
        input_lab = ('id', 'com.tencent.mm:id/b3b')
        send_lab = ('id', 'com.tencent.mm:id/ch')

        capture_timeout = 45

        def _back():
            self.driver.back()

        def _thumb():
            sa = self.webdriver_tools.find_elements(_sa)
            if sa:
                try:
                    # 排除找到的第一个可点赞的元素，滑动过程中它可能被遮盖，无法点击
                    sa = sa[random.randint(1, len(sa) - 1)] if len(sa) > 1 else sa[0]
                    self.webdriver_tools.click(sa)
                    btn = self.webdriver_tools.find_element(_btn)
                    self.webdriver_tools.click(btn)
                except:
                    pass

        def _own_moments(t):
            '''
            浏览自己的朋友圈
            :param t: 抓包开始时间
            :return:
            '''
            if self.webdriver_tools.is_element_exists('当前所在页面,朋友圈'):
                times = 0
                while True:
                    # 随机点赞
                    flag = random.randint(0, 1000)
                    if flag % 2 == 0:
                        _thumb()
                    if times > random.randint(1, 2):
                        break
                    # 滑动浏览
                    self.webdriver_tools.swipe_to('up')
                    time.sleep(1)
                    times += 1
                end_time = time.time() - t
                end_time = capture_timeout - int(end_time)
                return end_time
            else:
                return 0

        def _other_moments(t):
            '''
            浏览朋友的朋友圈
            :param t: 抓包的开始时间
            :return:
            '''
            def __choose_index(_list):
                if len(_list) == 1:
                    _index = 0
                else:
                    _index = random.randint(0, len(_list) - 1)

                return _index

            def __comment_or_thumb(flag):
                '''
                comment or thumb
                :param flag: 0:thumb, 1:comment
                :return:
                '''
                flag_dict = {0: _in_thumb, 1: _in_comment}
                ele = self.webdriver_tools.find_element(flag_dict.get(flag))
                if ele:
                    self.webdriver_tools.click(ele)
                if flag == 1:
                    time.sleep(1)
                    input_ele = self.webdriver_tools.find_element(input_lab)
                    time.sleep(1)
                    if input_ele:
                        self.webdriver_tools.send_keys(input_ele, '赞！')
                        ele_send = self.webdriver_tools.find_element(send_lab)
                        if ele_send:
                            self.webdriver_tools.click(ele_send)
                            time.sleep(2)
                _back()

            if self.webdriver_tools.is_element_exists('当前所在页面,朋友圈'):
                sweipe_times = random.randint(1, 3)
                for i in range(sweipe_times):
                    self.webdriver_tools.swipe_to('up')
                time.sleep(2)
                # 进入朋友的朋友圈
                others = self.webdriver_tools.find_elements(_other_frind_lab)
                if others:
                    _in_index = __choose_index(others)
                    self.webdriver_tools.click(others[_in_index])
                    # 浏览朋友圈，并点赞或评论
                    _moment = self.webdriver_tools.find_elements(_in_frind_page)
                    if _moment:
                        self.webdriver_tools.click(_moment[0])
                        time.sleep(2)
                        for i in range(sweipe_times):
                            self.webdriver_tools.swipe_to('up')
                        time.sleep(2)
                        more_moment = self.webdriver_tools.find_elements(_frind_more_moment)
                        if more_moment:
                            for index in range(len(more_moment)):
                                self.webdriver_tools.click(more_moment[index])
                                time.sleep(2)
                                flag = random.randint(0, 1)
                                __comment_or_thumb(flag)
                else:
                    return 2

                return int(time.time() - t)
            else:
                return 0

        lab_btns = self.webdriver_tools.find_elements(_lab_btns)
        if lab_btns:
            find_btn = lab_btns[2]
            self.webdriver_tools.click(find_btn)
            moments_btn = self.webdriver_tools.find_elements(_loc)
            if moments_btn:
                moments_btn = moments_btn[0]
                self.webdriver_tools.click(moments_btn)
                time.sleep(2)
                while True:
                    func_flag = random.randint(0, 1)
                    print('开始浏览{}'.format({0: '自己的朋友圈', 1: '朋友的朋友圈'}.get(func_flag)))
                    if func_flag == 0:
                        capture_timeout = 25
                    self.adb_tools.capture(self.meid, capture_timeout)
                    t = time.time()
                    func_flag = 1
                    if func_flag == 0:
                        end_time = _own_moments(t)
                    else:
                        end_time = _other_moments(t)
                        back_time = 0
                        while not self.webdriver_tools.is_element_exists('当前所在页面,朋友圈'):
                            if back_time > 3:
                                break
                            _back()
                            back_time += 1
                    if end_time > 0:
                        self.adb_tools.end_capture(self.meid, '微信朋友圈点赞', end_time)
                        # _back()
                    else:
                        self.adb_tools.end_capture_without_save(self.meid)
            else:
                self.adb_tools.end_capture_without_save(self.meid)
        else:
            self.adb_tools.end_capture_without_save(self.meid)

    def top_stories(self):
        '''
        浏览看一看
        :return:
        '''

    def games(self):
        '''
        微信游戏
        :return:
        '''

    def wechat_pay(self, payment_code):
        '''
        扫码付款, 付款二维码通过读取图片进行
        :param payment_code: 付款码图片地址
        :return:
        '''

    def on_wechat(self):
        if self.webdriver_tools.is_element_exists('华西第二医院'):
            return True
        else:
            return False


def create_input_text():
    with open('../config/inputText.log', 'r', encoding='utf-8') as f:
        return random.choice(f.readlines())


if __name__ == '__main__':
    wechat = WeChat()
    wechat.new_appium_session()
    time.sleep(3)

    def run():
        while True:
            if not wechat.on_wechat():
                quite_driver(wechat.driver)
                wechat.new_appium_session()
            # wechat.choose_friend('yest')
            # wechat.send_text_msg('yest', create_input_text())
            # wechat.back_to_main_page()
            # wechat.send_pic_msg('yest')
            # wechat.back_to_main_page()
            # wechat.send_video_msg('yest', 1)
            # wechat.back_to_main_page()
            # wechat.send_red_packet('yest', '测试红包', '258123')
            # wechat.back_to_main_page()
            # wechat.send_msg_with_voice('yest', 5000)
            # wechat.back_to_main_page()
            # wechat.send_emoticon('yest', '测试表情')
            # wechat.back_to_main_page()
            wechat.moments()
            # wechat.back_to_main_page()
    try:
        run()
    except:
        run()

import os

folder_path = r"E:\项目\yara\样本"
output_file = "output.txt"
output_diff_file = "diff_output.txt"

# 获取文件夹下的文件列表
file_names = os.listdir(folder_path)
if not os.path.exists(output_file):
    # 将文件名写入 txt 文件
    with open(output_file, "w") as f:
        for file_name in file_names:
            f.write(file_name + "\n")
else:
        # 读取对比文件中的文件名称
    with open(output_file, "r") as f:
        file_names_1 = set(f.read().splitlines())
    # 找出不同的文件名称并写入新的 txt 文件
    diff_file_names = file_names_1 - set(file_names)
    with open(output_diff_file, "w") as f:
        for file_name in diff_file_names:
            f.write(file_name + "\n")
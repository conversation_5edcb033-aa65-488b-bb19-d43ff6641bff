#!/usr/bin/env python
# -*- coding: utf-8 -*-


from base.crawl_tools import html_content
import json
from copy import deepcopy

'''
分类id提取地址:
url = "https://app.market.xiaomi.com/apm/category/mainlist?os=V12.0.3.0.QDGMIXM&sdk=29"
payload={}
headers = {
  'Host': 'app.market.xiaomi.com',
  'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 10; Mi MIX 2S MIUI/V12.0.3.0.QDGMIXM)'
}

分类下全量app列表请求地址
https://app.market.xiaomi.com/apm/toplist/category/14?combine=1&os=V12.0.3.0.QDGMIXM&sdk=29&stamp=0&page=0
base_url = https://app.market.xiaomi.com/
各分类app列表: (如果需要访问大分类下的二级分类，资源地址为：/apm/featured/分类id)
    /apm/toplist/category/
    分类id：
    14
    固定参数
    combine=1&os=V12.0.3.0.QDGMIXM&sdk=29&stamp=0&page=0
'''

base_url = 'https://app.market.xiaomi.com'
category_uri = '/apm/category/mainlist?os=V12.0.3.0.QDGMIXM&sdk=29'
apps_uri = '/apm/toplist/category/{}?combine=1&os=V12.0.3.0.QDGMIXM&sdk=29&stamp=0&page='
sub_apps_uri = '/apm/featured/{}?combine=1&os=V12.0.3.0.QDGMIXM&sdk=29&stamp=0&page='
header = {'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 10; Mi MIX 2S MIUI/V12.0.3.0.QDGMIXM)'}


def category_info():
    data_list = list()
    cate_dict = dict()
    url = base_url + category_uri
    raw_rest, encode, cookie = html_content(url, header=header)
    json_data = json.loads(raw_rest.decode(encode))
    item_list = json_data.get('list')
    for _ in item_list:
        data = _.get('data')
        category = data.get('tableTitle') if data.get('tableTitle') else '应用分类'
        cate_dict['cate_name'] = category
        cate_dict['cate_list'] = list()
        for item in data.get('rowList'):
            tmp_dict = dict()
            cate_id = item.get('leftCell').get('cellUrl').split('/')[-1]
            tmp_dict['second_cate_name'] = item.get('leftCell').get('cellText')
            tmp_dict['second_url'] = base_url + apps_uri.format(cate_id)
            tmp_dict['sub_cate_list'] = list()
            third_category = item.get('rightCellList')
            for j in third_category:
                sub_tmp_dict = dict()
                third_cate_id = j.get('cellUrl').split('/')[-1]
                sub_tmp_dict['third_cate_name'] = j.get('cellText')
                sub_tmp_dict['third_url'] = base_url + sub_apps_uri.format(third_cate_id)
                tmp_dict['sub_cate_list'].append(deepcopy(sub_tmp_dict))
            cate_dict['cate_list'].append(deepcopy(tmp_dict))
        data_list.append(deepcopy(cate_dict))

    # data_list = json.dumps(data_list, ensure_ascii=False)

    # with open('category.json', 'w') as f:
    #     f.write(data_list)
    return data_list


def crawl_urls(category):
    url_list = list()
    for item in category:
        for url in [j.get('second_url') for j in item['cate_list']]:
            url_list.append(url)
    return url_list


def parse_detail(json_data):
    for items in json_data.get('list'):
        for app in items.get('data').get('listApp'):
            app_id = app.get('appId')
            package_name = app.get('packageName')
            app_name = app.get('displayName')
            owner = app.get('publisherName')
            app_version = app.get('versionName')
            update_time = app.get('updateTime')
            app_size = app.get('apkSize')
            category_name = app.get('level1CategoryName')
            sub_category_name = app.get('level2CategoryName')
            download_count = app.get('downloadCount')
            print('app_id: {}, package_name: {}, app_name: {}, owner: {}, app_version: {}, update_time: {}, '
                  'app_size: {}, category_name: {}, sub_category_name: {}, download_count: {}'.
                  format(app_id, package_name, app_name, owner, app_version, update_time, app_size,
                         category_name, sub_category_name, download_count))


def html_info(urls):
    not_found = set()
    for url in urls:
        page_num = 0
        err_resp = 0
        while True:
            raw_rest, encode, cookie = html_content(url + str(page_num), header=header)
            if raw_rest:
                err_resp = 0
                json_data = json.loads(raw_rest.decode(encode))
                if not json_data.get('hasMore'):
                    continue
                else:
                    parse_detail(json_data)
                page_num += 1
            else:
                not_found.add(url)
                if err_resp > 10:
                    with open('noresp.log', 'w') as f:
                        for url in not_found:
                            f.write(url)
                            f.write('\n')
                    break
                err_resp += 1


if __name__ == '__main__':
    category = category_info()
    urls = crawl_urls(category)
    html_info(urls)

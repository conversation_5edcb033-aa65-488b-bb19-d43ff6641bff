# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   common.py
@Time    :   2023/5/24 9:28
"""
import os
import csv
import re
import json
import time
import hashlib
import asyncio
import shutil
import subprocess
import difflib
import paramiko
import datetime
from datetime import timedelta
from multiprocessing import Lock
import socket
from xml.dom import minidom
from .logger import log, ERROR, WARN
from main.config import config, system
from openpyxl import Workbook, load_workbook
from .mysql_helper import MySqliteHelper


def fixed_writexml(self, writer, indent="", addindent="", newl=""):
    """由于minidom默认的writexml()函数在读取一个xml文件后，
    修改后重新写入如果加了newl='\n',会将原有的xml中写入多余的行,因此使用下面这个函数来代替"""
    writer.write(indent+"<" + self.tagName)
    attrs = self._get_attributes()
    a_names = attrs.keys()
    sorted(a_names)

    for a_name in a_names:
        writer.write(" %s=\"" % a_name)
        minidom._write_data(writer, attrs[a_name].value)
        writer.write("\"")
    if self.childNodes:
        if len(self.childNodes) == 1 \
          and self.childNodes[0].nodeType == minidom.Node.TEXT_NODE:
            writer.write(">")
            self.childNodes[0].writexml(writer, "", "", "")
            writer.write("</%s>%s" % (self.tagName, newl))
            return
        writer.write(">%s"%(newl))
        for node in self.childNodes:
            if node.nodeType is not minidom.Node.TEXT_NODE:
                node.writexml(writer,indent+addindent,addindent,newl)
        writer.write("%s</%s>%s" % (indent,self.tagName,newl))
    else:
        writer.write("/>%s"%(newl))


minidom.Element.writexml = fixed_writexml


def create_dir(path: str):
    try:
        if not os.path.exists(path):
            create_dir(os.path.split(path)[0])
        else:
            return
        os.mkdir(path)
    except Exception as e:
        log.log_screen_store(f"创建目录失败-{str(e)}")


def _encoding(info: bytes, err: bytes) -> (str, str):
    codes = ["utf-8", "gbk"]
    for _encode in codes:
        try:
            return str(info, encoding=_encode), str(err, encoding=_encode)
        except Exception:
            pass
    return str(info), str(err)


def execute(cmd: str, timeout=config.cmd_execute_timeout, asyn=False) -> (str, str) or subprocess.Popen:
    """执行命令"""
    out, err = "", ""
    try:
        result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  shell=True)
        if not asyn:
            out, err = result.communicate(timeout=timeout)
            return _encoding(out, err)
        else:
            return result
    except Exception as e:
        log.log_screen_store(f"执行命令错误{cmd}-{str(e)}", level=ERROR)
        return out, err if not asyn else None


def convert_eth(pcap: str) -> str:
    """转换eth头"""
    if system == "Linux":
        eth_convert = os.path.join(config.BASE_PATH, "config", config.ETH_CONVERT)
        convert_cmd = f"""{eth_convert} {pcap} b2:36:67:5e:40:21 68:91:d0:65:3c:37 """
        out, err = execute(convert_cmd)
        if err:
            log.log_screen_store(f"{pcap} 转换ETH头异常", level=WARN)
            return pcap
        eth_pcap = pcap.replace(".pcap", ".eth.pcap")
        return eth_pcap
    return pcap


def get_processed_apps2(file_name: str) -> list:
    """获取已处理过的应用"""
    apps = []
    path = os.path.join(config.local_tmp_dir, file_name)
    if not os.path.exists(path):
        return apps
    with open(path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                l_class_id = row.index(config.L_CLASS) if config.L_CLASS in row else -1
                if l_class_id == -1:
                    log.log_screen_store(f"未获取到已处理表的表头索引（package）")
                    return []
                continue
            apps.append(row[l_class_id])
    return apps


def get_processed_apps(file_name: str) -> list:
    """获取已处理过的应用"""
    apps = []
    path = os.path.join(config.local_tmp_dir, file_name)
    if not os.path.exists(path):
        return apps
    wb = None
    try:
        wb = load_workbook(path)
        sheet = wb.active
        for row in sheet.iter_rows(min_row=2):
            if row and row[0].value:
                apps.append(row[0].value)
    except Exception as e:
        log.log_screen_store(f"读取已处理{file_name}文件失败", level=ERROR)
    finally:
        if wb:
            wb.close()
    return apps


def get_valid_apps() -> list:
    """获取有效的app"""
    data = []
    path = os.path.join(config.BASE_PATH, 'config', config.valid_apps_filename)
    if not os.path.exists(path):
        log.log_screen_store(path, level=ERROR)
        return data
    with open(path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        name_id, l_class_id, b_class_id, package_id, app_id, url_id = -1, -1, -1, -1, -1, -1
        for index, row in enumerate(reader):
            if index == 0:
                name_id = row.index(config.NAME) if config.NAME in row else -1
                l_class_id = row.index(config.L_CLASS) if config.L_CLASS in row else -1
                b_class_id = row.index(config.B_CLASS) if config.B_CLASS in row else -1
                package_id = row.index(config.PACKAGE) if config.PACKAGE in row else -1
                app_id = row.index(config.APP_ID) if config.APP_ID in row else -1
                url_id = row.index(config.APP_URL) if config.APP_URL in row else -1
                if name_id == -1 or l_class_id == -1 or b_class_id == -1 or package_id == -1 \
                        or app_id == -1 or url_id == -1:
                    log.log_screen_store(f"未获取到完整表头索引，请检查{path}", level=WARN)
                    return data
                continue
            data.append({config.NAME: row[name_id], config.L_CLASS: row[l_class_id],
                         config.B_CLASS: row[b_class_id], config.PACKAGE: row[package_id],
                         config.APP_ID: row[app_id], config.APP_URL: row[url_id]})
    return data


def write_valid_apps(data: list):
    """写入有效的app"""
    tmp_path = os.path.join(config.BASE_PATH, "config", config.valid_apps_filename)
    csv_header = [config.NAME, config.L_CLASS, config.B_CLASS, config.PACKAGE, config.APP_ID, config.APP_URL]
    if not os.path.exists(tmp_path):
        with open(tmp_path, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(csv_header)
    with open(tmp_path, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in data:
            writer.writerow(row)


def get_apps_list():
    """获取应用列表"""
    apps = {}
    path = os.path.join(config.BASE_PATH, 'config', config.apps_filename)
    log.log_screen_store(f"parse app list: {path}")
    if not os.path.exists(path):
        log.log_screen_store(path, level=ERROR)
        return
    with open(path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        name_id, l_class_id, b_class_id, remark_id = -1, -1, -1, -1
        package_id, pcap_id = -1, -1
        l_en_name_id, b_name_id, b_en_name_id = -1, -1, -1
        for index, row in enumerate(reader):
            if index == 0:
                name_id = row.index(config.NAME) if config.NAME in row else -1
                l_class_id = row.index(config.L_CLASS) if config.L_CLASS in row else -1
                b_class_id = row.index(config.B_CLASS) if config.B_CLASS in row else -1
                remark_id = row.index(config.REMARK) if config.REMARK in row else -1
                package_id = row.index(config.PACKAGE) if config.PACKAGE in row else -1
                pcap_id = row.index(config.PCAP) if config.PCAP in row else -1
                l_en_name_id = row.index(config.L_EN_NAME) if config.L_EN_NAME in row else -1
                b_name_id = row.index(config.B_NAME) if config.B_NAME in row else -1
                b_en_name_id = row.index(config.B_EN_NAME) if config.B_EN_NAME in row else -1
                if name_id == -1 or l_class_id == -1 or b_class_id == -1 or l_en_name_id == -1 \
                        or b_name_id == -1 or b_en_name_id == -1:
                    log.log_screen_store(f"未获取到完整表头索引，请检查{path}", level=WARN)
                    return apps
                continue
            if row[b_class_id] not in config.IGNORE_NETWORK_PROTOCOL:
                remark = row[remark_id] if remark_id != -1 else ""
                package = row[package_id] if package_id != -1 else ""
                pcap = row[pcap_id] if pcap_id != -1 else ""
                apps[row[name_id]] = {config.NAME: row[name_id], config.L_CLASS: row[l_class_id],
                                      config.B_CLASS: row[b_class_id], config.REMARK: remark,
                                      config.PACKAGE: package, config.PCAP: pcap, config.B_EN_NAME: row[b_en_name_id],
                                      config.B_NAME: row[b_name_id], config.L_EN_NAME: row[l_en_name_id]}
    return apps

def _update_pcap(sql_helper: MySqliteHelper, row_dict: dict, table: str):
    data = sql_helper.query(f"select * from stream_recognition where value={row_dict[config.L_CLASS]}")
    if data:
        sql_helper.update(f"update stream_recognition set package='{row_dict[config.PACKAGE]}', pcap_name='{row_dict[config.PCAP]}' where value={row_dict[config.L_CLASS]}")
        sql_helper.update(f"update {table} set identify_flag=0 where l_class={row_dict[config.L_CLASS]} and identify_script==1")


def _update_table(sql_helper: MySqliteHelper, row_dict: dict, table1: str, table2: str, table_id: str):
    cur_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')
    cmd1 = f"select l_class from {table1} where id={row_dict[config.ID]}"
    data = sql_helper.query(cmd1)
    if not data or not data[0] or data[0][0] != row_dict[config.L_CLASS]:
        log.log_screen_store(f"在{table1}表未查到id={row_dict[config.ID]}且l_class={row_dict[config.L_CLASS]}的数据",
                             level=WARN)
        sql_helper.close()
        return
    result = "成功" if row_dict[config.IS_BLOCKED] == "Y" else "失败"
    ins = 1 if row_dict[config.IS_BLOCKED] == "Y" else 2
    mod = 1 if row_dict[config.IS_BLOCKED] == "Y" else 0
    cmd2 = "update {} set package='{}', priority=1, result='{}', remark='{}' where id={}"
    f_cmd2 = cmd2.format(table1, row_dict[config.PACKAGE], result, row_dict[config.REMARK], row_dict[config.ID])
    sql_helper.update(f_cmd2)

    cmd3 = ("insert into {} (l_class, mobile_ip, router_host, router_port, extend_device, "
            "created_at, cur_result, start_time, end_time, {}) values "
            "({}, '{}', '{}', {}, '{}', '{}', {}, '{}', '{}', {})")
    f_cmd3 = cmd3.format(table2, table_id, row_dict[config.L_CLASS],
                         row_dict[config.MOBILE_IP], row_dict[config.ROUTER_HOST], row_dict[config.ROUTER_PORT],
                         row_dict[config.EXTEND_DEVICE], cur_time, ins, row_dict[config.START_TIME],
                         row_dict[config.END_TIME], row_dict[config.ID])
    sql_helper.update(f_cmd3)

    cmd4 = "select id from task where status==3"
    data1 = sql_helper.query(cmd4)
    if data1:
        cb = [f'task_id = {item[0]}' for item in data1]
        scb = " or ".join(cb)
        cmd5 = "update task_record set result={}, repaired={} where ({}) and appid={} and platform={} and result=0"
        f_cmd5 = cmd5.format(ins, mod, scb, row_dict[config.ID], config.platform_type)
        sql_helper.update(f_cmd5)
        if mod:
            cmd6 = "update task_record set repaired=1 where ({}) and appid={} and platform={}"
            f_cmd6 = cmd6.format(scb, row_dict[config.ID], config.platform_type)
            sql_helper.update(f_cmd6)


def update_script_db(row_dict: dict):
    sql_helper = MySqliteHelper()
    if not sql_helper.connect(config.script_db):
        return
    if config.platform_type == 1:
        _update_table(sql_helper, row_dict, 'cn_android', 'cn_android_record', 'cn_android_id')
    if config.platform_type == 2:
        _update_table(sql_helper, row_dict, 'en_android', 'en_android_record', 'en_android_id')
    if config.platform_type == 3:
        _update_table(sql_helper, row_dict, 'cn_ios', 'cn_ios_record', 'cn_ios_id')
    if config.platform_type == 4:
        _update_table(sql_helper, row_dict, 'en_ios', 'en_ios_record', 'en_ios_id')
    if config.platform_type == 5:
        _update_pcap(sql_helper, row_dict, 'cn_android')
    if config.platform_type == 6:
        _update_pcap(sql_helper, row_dict, 'en_android')
    sql_helper.close()


def write_excel(row_dict: dict, file_name: str, lock: Lock):
    """写入分析结果表"""
    lock.acquire()
    path = os.path.join(config.local_tmp_dir, file_name)
    try:
        csv_header = [config.NAME, config.L_CLASS, config.PACKAGE, config.REMARK, config.PCAP_BEFORE, config.PCAP_AFTER,
                      config.BEFORE, config.AFTER, config.WEIXIN_LOGIN, config.SCREENSHOT, config.IS_BLOCKED,
                      config.SUSPICIOUS_DOMAINS]
        if not os.path.exists(path):
            with open(path, 'w', encoding="UTF-8-sig", newline='') as f:
                writer = csv.writer(f)
                writer.writerow(csv_header)
        with open(path, 'a+', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            row = []
            for head in csv_header:
                row.append(row_dict.get(head, ""))
            writer.writerow(row)
    except Exception as e:
        log.log_screen_store(f"写入分析结果表异常-{path}-{str(e)}", level=ERROR)

def write_xlsx(row_dict: dict, file_name: str, lock: Lock):
    """写入分析结果表"""
    lock.acquire()
    path = os.path.join(config.local_tmp_dir, file_name)
    # 判断工作簿是否存在
    try:
        workbook = load_workbook(path)
    except FileNotFoundError:
        workbook = Workbook()
    sheet = workbook.active
    try:
        excle_header = [config.NAME, config.APP_ID, config.L_CLASS, config.B_CLASS, config.PACKAGE,
                      config.REMARK, config.PCAP, config.BEFORE, config.AFTER]
        # 写入表头
        if sheet.max_row == 1:  # 如果工作表为空，则写入表头
            sheet.append(excle_header)
        # 写入数据并添加超链接
        current_row = sheet.max_row + 1
        for j, col_name in enumerate(excle_header, start=1):
            cell_value = row_dict.get(col_name, "")
            sheet.cell(row=current_row, column=j, value=cell_value)
            # 添加超链接
            if col_name == "after" or col_name == "before":
                link = cell_value
                sheet.cell(row=current_row, column=j).hyperlink = link
        current_row += 1
        # 调整列宽以适应内容
        for column in sheet.columns:
            max_length = 0
            column = [cell for cell in column]
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(cell.value)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            sheet.column_dimensions[column[0].column_letter].width = adjusted_width
        # 保存工作簿
        workbook.save(path)
    except Exception as e:
        log.log_screen_store(f"写入分析结果表异常-{path}-{str(e)}", level=ERROR)
    lock.release()


def write_json(json_name: str, data: dict) -> str:
    feature_path = os.path.join(config.local_tmp_dir, "feature")
    create_dir(feature_path)
    path = os.path.join(feature_path, json_name)
    try:
        json_str = json.dumps(data, indent=4)
        with open(path, 'w') as json_file:
            json_file.write(json_str)
    except Exception as e:
        log.log_screen_store(f"记录特征文件异常-{str(e)}", level=ERROR)
    return path


def transform_app_name(app_name: str) -> str:
    """转换应用名称"""
    try:
        return hashlib.md5(app_name.encode("utf-8")).hexdigest()
    except Exception as e:
        log.log_screen_store("应用名称转换异常", level=WARN)
        text = str(time.time())
        return hashlib.md5(text.encode("utf-8")).hexdigest()


def feature_extract(pcap: str) -> dict:
    """解析pcap包提取特征"""
    if system == "Linux":
        loop = None
    else:
        loop = asyncio.ProactorEventLoop()
        asyncio.set_event_loop(loop)
    # packets = pyshark.FileCapture(pcap, tshark_path=config.tshark, display_filter='http or tls', eventloop=loop)
    packets = {}
    packets.load_packets()
    features = {"server_name": [], "url": [], "content-type": [], "host": [], "location": [], "x-online-host": [],
                "referer": [], "user-agent": [], "x-requested-with": [], "cookie": [], "appid": [],
                "q-ua2": [], "set-cookie": [], "x-cache-lookup": []}
    for packet in packets:
        highest_layer = packet.highest_layer
        if highest_layer == "TLS" and hasattr(packet[highest_layer], 'handshake_extensions_server_name'):
            server_name = str(packet[highest_layer].handshake_extensions_server_name).replace(config.LF, "")
            if server_name and server_name not in features["server_name"]:
                features["server_name"].append(server_name)
        if highest_layer != "TLS":
            if hasattr(packet.http, 'request_full_uri'):
                uri = str(packet.http.request_full_uri)
                if uri and uri not in features["url"]:
                    features["url"].append(uri)
            if hasattr(packet.http, 'host'):
                host = str(packet.http.host)
                if host and host not in features["host"]:
                    features["host"].append(host)
            if hasattr(packet.http, 'location'):
                location = str(packet.http.location)
                if location and location not in features["location"]:
                    features["location"].append(location)
            if hasattr(packet.http, 'referer'):
                referer = str(packet.http.referer)
                if referer and referer not in features["referer"]:
                    features["referer"].append(referer)
            if hasattr(packet.http, 'user_agent'):
                user_agent = str(packet.http.user_agent)
                if user_agent and user_agent in features:
                    features["user_agent"].append(user_agent)
            if hasattr(packet.http, 'cookie'):
                cookie = str(packet.http.cookie)
                if cookie and cookie not in features["cookie"]:
                    features["cookie"].append(cookie)
            x_requested_with = packet.http.get_field_by_showname('X-Requested-With')
            if x_requested_with:
                value = x_requested_with.showname_value.replace(config.LF, "")
                if value not in features["x-requested-with"]:
                    features["x-requested-with"].append(value)
            app_id = packet.http.get_field_by_showname('appId')
            if app_id:
                value = app_id.showname_value
                if value not in features["appid"]:
                    features["appid"].append(value)
            q_ua2 = packet.http.get_field_by_showname('Q-UA2')
            if q_ua2:
                value = q_ua2.showname_value
                if value not in features["q-ua2"]:
                    features["q-ua2"].append(value)
            x_online_host = packet.http.get_field_by_showname('x-online-host')
            if x_online_host:
                value = x_online_host.showname_value.replace(config.LF, "")
                if value not in features["x-online-host"]:
                    features["x-online-host"].append(value)
            x_cache_lookup = packet.http.get_field_by_showname('X-Cache-Lookup')
            if x_cache_lookup:
                value = x_cache_lookup.showname_value.replace(config.LF, "")
                if value not in features["x-cache-lookup"]:
                    features["x-cache-lookup"].append(value)
            set_cookie = packet.http.get_field_by_showname('Set-Cookie')
            if set_cookie:
                value = set_cookie.showname_value.replace(config.LF, "")
                if value not in features["set-cookie"]:
                    features["set-cookie"].append(value)
            if hasattr(packet.http, 'content_type'):
                content_type = str(packet.http.content_type)
                if content_type and content_type not in features["content-type"]:
                    features["content-type"].append(content_type)
    packets.close()
    return features


class FileParsing(object):
    def __init__(self):
        self.attribute = dict()
        self.init_template()
        self.sniffer_template = self.attribute["sniffer"]

    @staticmethod
    def read_template():
        file_name = os.path.join(os.path.abspath('.'), 'template.xml')
        data = ""
        try:
            fp = open(file_name, 'r', encoding="utf-8")
            _data = fp.read()
            data = _data.replace("\t", " " * 3)
        except IOError:
            log.log_screen_store(f"读取特征库模板文件错误", level=ERROR)
        return data

    def init_template(self):
        # """打开选择并读取文件"""
        """获取模板文件路径"""
        file_name = os.path.join(config.BASE_PATH, 'config', 'template.xml')
        try:
            if file_name:
                """读取模板文件并作为列表返回文件中的所有行"""
                with open(file_name, 'r', encoding='utf-8') as f:
                    attribute_list = f.readlines()
                """创建临时字典存储数据"""
                tmp_dict = dict()
                for i in range(0, len(attribute_list)-2):
                    if 'snifferCfg' in attribute_list[i]:
                        continue
                    elif 'snf_version' in attribute_list[i]:
                        snf_list = re.findall(r' (.*?)="', attribute_list[i])
                        snf_value_list = dict([(key, "") for key in snf_list])
                        self.attribute['snf_version'] = {'property': snf_value_list, 'type': 0}
                    elif 'cfg_item' in attribute_list[i]:
                        cfg_list = re.findall(r' (.*?)="', attribute_list[i])
                        cfg_value_list = dict([(key, "") for key in cfg_list])
                        self.attribute['cfg_item'] = {'property': cfg_value_list, 'type': 0}
                    elif 'sniffer ' in attribute_list[i]:
                        main_list = re.findall(r' (.*?)="', attribute_list[i])
                        main_value_list = dict([(key, "") for key in main_list])
                        tmp_dict['property'] = main_value_list
                    else:
                        list1 = re.findall(r' (.*?)="', attribute_list[i])
                        value_list = dict([(key, "") for key in list1])
                        if "</" in attribute_list[i]:
                            keys = re.findall(r'<(.*?)>', attribute_list[i])
                            if keys:
                                key = keys[0]
                                if " " in key:
                                    key = key.split(' ')[0]
                                tmp_dict[key] = {"type": 1, "property": value_list, "value": ""}
                        else:
                            keys = re.findall(r'<(.*?) ', attribute_list[i])
                            if keys:
                                key = keys[0]
                                tmp_dict[key] = {"type": 0, "property": value_list}
                self.attribute['sniffer'] = tmp_dict
        except Exception as e:
            log.log_screen_store(f"解析特征库模板文件异常-{str(e)}", level=ERROR)

    def read_xml(self, file_path: str):
        result_sniffer = dict()
        try:
            domTree = minidom.parse(file_path)
            # 文档根元素
            rootNode = domTree.documentElement
            """所有规则体"""
            sniffers = rootNode.getElementsByTagName("sniffer")
            sniffer_list = list()
            """遍历每个规则体属性"""
            for sniffer in sniffers:
                """创建一个字典保存每个规则体属性"""
                sniffer_attr = dict()
                property_attr = dict()
                for k in self.sniffer_template.keys():
                    if k == "property":
                        for key in self.sniffer_template[k].keys():
                            if sniffer.hasAttribute(key):
                                property_attr[key] = sniffer.getAttribute(key)
                            else:
                                continue
                        sniffer_attr[k] = property_attr
                    else:
                        results = sniffer.getElementsByTagName(k)
                        if results:
                            for result in results:
                                tmp_attr = dict()
                                for key in self.sniffer_template[k]["property"].keys():
                                    if result.hasAttribute(key):
                                        tmp_attr[key] = result.getAttribute(key)
                                    else:
                                        continue
                                if self.sniffer_template[k]["type"] == 1:
                                    if result.childNodes:
                                        value = result.childNodes[0].data
                                        tmp_attr["_text"] = value
                                if k in sniffer_attr.keys():
                                    sniffer_attr[k]["property"].append(tmp_attr)
                                else:
                                    sniffer_attr[k] = dict()
                                    sniffer_attr[k]["property"] = [tmp_attr]
                                    sniffer_attr[k]["type"] = self.sniffer_template[k]["type"]
                sniffer_list.append(sniffer_attr)
            result_sniffer["sniffers"] = sniffer_list
        except Exception as e:
            log.log_screen_store(f"解析特征库文件异常-{str(e)}", level=ERROR)
        return result_sniffer

    @staticmethod
    def write_xml(file_name: str, data: dict, lock: Lock):
        file_path = os.path.join(config.local_tmp_dir, file_name)
        lock.acquire()
        if os.path.exists(file_path):
            """1. 创建dom树对象"""
            doc = minidom.parse(file_path)
            """文档根元素"""
            root_node = doc.documentElement
        else:
            """1. 创建dom树对象"""
            doc = minidom.Document()
            """2. 创建根结点，并用dom对象添加根结点"""
            root_node = doc.createElement("snifferCfg")
            doc.appendChild(root_node)

        """是否创建sniffer版本节点信息"""
        if 'snf_version' in data.keys():
            snf_version = doc.createElement("snf_version")
            for key, value in data['snf_version']['property'].items():
                snf_version.setAttribute(key, value)
            root_node.appendChild(snf_version)

        # """是否创建sniffer配置节点信息"""
        # if 'cfg_item' in self.data.keys():
        #     cfg_item = doc.createElement("cfg_item")
        #     for key, value in self.data['cfg_item']['property'].items():
        #         cfg_item.setAttribute(key, value)
        #     root_node.appendChild(cfg_item)

        for sniffer in data['sniffers']:
            sniffer_node = doc.createElement("sniffer")
            for k in sniffer.keys():
                """设置sniffer节点属性"""
                if k == "property":
                    for key, value in sniffer[k].items():
                        if value:
                            sniffer_node.setAttribute(key, value)
                        else:
                            continue
                else:
                    """创建并设置sniffer子节点"""
                    if k in sniffer.keys():
                        if "property" in sniffer[k].keys():
                            for content in sniffer[k]['property']:
                                key_node = doc.createElement(k)
                                for key, value in content.items():
                                    if value and key != '_text':
                                        key_node.setAttribute(key, value)
                                if sniffer[k]["type"] == 1:
                                    _text_value = doc.createTextNode(content['_text'])
                                    key_node.appendChild(_text_value)
                                sniffer_node.appendChild(key_node)
            root_node.appendChild(sniffer_node)
        """写入xml文件"""
        with open(file_path, "w", encoding="utf-8") as f:
            doc.writexml(f, addindent='\t', newl='\n', encoding='utf-8')
        lock.release()


def similarity(first: str, second: str) -> float:
    return difflib.SequenceMatcher(None, first, second).quick_ratio()


def file_remove(file: str):
    """删除文件"""
    try:
        if os.path.exists(file):
            os.remove(file)
    except OSError as e:
        log.log_screen_store("remove {} failed - {}".format(file, str(e)), level=WARN)


def clear_dir(dir_path: str):
    """清空目录"""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        file_list = os.listdir(dir_path)
        for file_name in file_list:
            file_path = os.path.join(dir_path, file_name)
            file_remove(file_path)


class Router(object):

    def __init__(self, device_info: dict):
        self.ssh = None
        self.device_info = device_info

    def connect(self) -> bool:
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.WarningPolicy())
            self.ssh.connect(self.device_info["router_host"], self.device_info["router_port"],
                             self.device_info["router_user"], self.device_info["router_pwd"], timeout=30)
            return True
        except Exception as e:
            log.log_screen_store(
                f"connect {self.device_info['router_host']}:{self.device_info['router_port']} failed {str(e)}",
                level=ERROR)
            return False

    def execute(self, cmd: str) -> bool:
        try:
            stdin, stdout, stderr = self.ssh.exec_command(cmd)
            return True if not stderr.readlines() else False
        except Exception as e:
            log.log_screen_store(f"execute {cmd} failed {str(e)}", level=ERROR)
            return False

    def exe_invoke_shell(self, appid: str, clear: int = 0) -> bool:
        try:
            router_index = self.device_info.get("router_index")
            chan = self.ssh.invoke_shell()
            chan.settimeout(10)
            chan.send('enable' + '\n')
            time.sleep(1)
            chan.send(self.device_info["router_enable_pwd"] + '\n')
            time.sleep(1)
            chan.send('config' + '\n')
            time.sleep(1)
            chan.send(f'ip access-list extended {self.device_info.get("extend_device")}' + '\n')
            time.sleep(1)
            if clear:
                cmd = f"no deny ip any any si-appid {appid}"
                chan.send(cmd + '\n')
                time.sleep(1)
            else:
                cmd = f"no {router_index}"
                chan.send(cmd + '\n')
                time.sleep(1)
                chan.send(f'{router_index} deny ip any any si-appid {appid}' + '\n')
                time.sleep(1)
            chan.send('exit' + '\n')
            chan.send('exit' + '\n')
            return True
        except Exception as e:
            log.log_screen_store(f"规则下发异常 {str(e)}", level=ERROR)
            return False

    def close(self):
        if self.ssh:
            try:
                self.ssh.close()
            except Exception as e:
                pass


class ScpClient(object):

    def __init__(self):
        self.client = None
        self.scp = None

    def connect(self, server_ip: str, username: str, password) -> bool:
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        flag = True
        try:
            self.client.connect(server_ip, 22, username, password)
            self.scp = self.client.open_sftp()
        except Exception as e:
            log.log_screen_store(f"scp 连接失败", level=ERROR)
            flag = False
        return flag

    def transmission(self, local_path: str, remote_path: str) -> bool:
        flag = True
        try:
            self.scp.put(local_path, remote_path)
        except Exception as e:
            log.log_screen_store(f"上传文件{local_path}失败", level=WARN)
            flag = False
        return flag

    def download(self, remote_path: str, local_path: str) -> bool:
        flag = True
        try:
            self.scp.get(remote_path, local_path)
        except Exception as e:
            flag = False
        return flag

    def close(self):
        if self.scp:
            self.scp.close()
        if self.client:
            self.client.close()


def get_socket_client():
    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    while True:
        try:
            client_socket.connect((config.server_ip, config.server_port))
            return client_socket
        except socket.error as e:
            log.log_screen_store(f"连接服务端（config.server_ip）失败，请确保服务端启动", level=ERROR)
            time.sleep(20)


def search_air(l_class: str):
    """根据小类ID查找airtest工程"""
    path = os.path.join(config.BASE_PATH, 'plugins')

    def search(current_dir: path):
        for item in os.listdir(current_dir):
            tmp_file = os.path.join(current_dir, item)
            if os.path.isdir(tmp_file):
                if tmp_file.endswith(f'{l_class}.air'):
                    return tmp_file
                else:
                    result = search(tmp_file)
                    if result:
                        return result

    return search(path)


def copy_remove(source: str = "", destination: str = "", copy: bool = True):
    tmp = ["common.py", "account.json"]
    if copy:
        for item in tmp:
            suc = os.path.join(source, item)
            dst = os.path.join(destination, item)
            try:
                shutil.copy(suc, dst)
            except Exception as e:
                pass
    else:
        for item in tmp:
            dst = os.path.join(destination, item)
            file_remove(dst)




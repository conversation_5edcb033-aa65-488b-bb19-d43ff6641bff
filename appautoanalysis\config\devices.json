{"capture_devices": {"DQAUBQLVQWZDLVT8": {"uuid": "DQAUBQLVQWZDLVT8", "xiaomi": "com.xiaomi.market", "yingyongbao": "com.tencent.android.qqdownloader", "google_play": "com.android.vending", "apkpure": "com.apkpure.aegon", "platform": "Android", "uri": "", "ip": "127.0.0.1", "port": "4723", "router_host": "***************", "router_port": 22, "router_user": "admin", "router_pwd": "zaq1,lp-", "router_enable_pwd": "zaq1,lp-", "extend_device": "t1", "browser": {"app_package": "com.android.chrome", "app_activity": "com.google.android.apps.chrome.Main", "automationName": "appium", "browserName": "chrome", "chromedriverExecutable": "D:\\software\\chromedriver_win32\\chromedriver.exe"}}, "INW8FEZHOVWGXSH6": {"uuid": "INW8FEZHOVWGXSH6", "xiaomi": "com.xiaomi.market", "yingyongbao": "com.tencent.android.qqdownloader", "google_play": "com.android.vending", "apkpure": "com.apkpure.aegon", "platform": "Android", "uri": "", "ip": "127.0.0.1", "port": "4723", "mobile_ip": "************", "delay": 3600, "router_host": "***************", "router_port": 22, "router_user": "admin", "router_pwd": "zaq1,lp-", "router_enable_pwd": "zaq1,lp-", "extend_device": "t1", "browser": {"app_package": "com.android.chrome", "app_activity": "com.google.android.apps.chrome.Main", "automationName": "appium", "browserName": "chrome", "chromedriverExecutable": "D:\\software\\chromedriver_win32\\chromedriver.exe"}}, "894XKRBIT8I7QWMZ": {"uuid": "894XKRBIT8I7QWMZ", "xiaomi": "com.xiaomi.market", "yingyongbao": "com.tencent.android.qqdownloader", "google_play": "com.android.vending", "apkpure": "com.apkpure.aegon", "platform": "Android", "uri": "", "ip": "127.0.0.1", "port": "4723", "router_host": "***************", "router_port": 22, "router_user": "admin", "router_pwd": "zaq1,lp-", "router_enable_pwd": "zaq1,lp-", "extend_device": "t1", "browser": {"app_package": "com.android.chrome", "app_activity": "com.google.android.apps.chrome.Main", "automationName": "appium", "browserName": "chrome", "chromedriverExecutable": "D:\\software\\chromedriver_win32\\chromedriver.exe"}}, "00008110-001C1DD026BA201E": {"uuid": "00008110-001C1DD026BA201E", "app_store": "com.apple.AppStore", "platform": "IOS", "app_store_pwd": "148142Zhou", "uri": "iOS:///**************:8100", "ip": "127.0.0.1", "port": "4723", "router_host": "**************", "router_port": 22, "router_user": "root", "router_pwd": "admin", "router_enable_pwd": "admin", "extend_device": "brd_test_2"}}, "analysis_devices": {"69f61036": {"udid": "69f61036", "app_package": "com.xiaomi.market", "app_activity": "com.xiaomi.market.ui.MarketTabActivity", "platformName": "Android", "automationName": "uiautomator2", "newCommandTimeout": 60000, "adbExecTimeout": 400000, "unicodeKeyboard": true, "resetKeyboard": false, "noReset": false, "ip": "127.0.0.1", "port": "4723", "phone": "13320985027", "browser": {"app_package": "com.android.chrome", "app_activity": "com.google.android.apps.chrome.Main", "automationName": "appium", "browserName": "chrome", "chromedriverExecutable": "D:\\software\\chromedriver_win32\\chromedriver.exe"}}}}
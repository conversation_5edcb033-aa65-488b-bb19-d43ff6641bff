# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   fake_pcap.py
@Time    :   2023/6/25 14:19
"""
import os
import re
import csv
import subprocess
from random import choice
import dpkt
from xml.dom import minidom

eth_convert = "/var/log/file_yara/eth_convert"
result_file = "/var/log/file_yara/analysis_result-2023-06-22-134044.csv"
new_resule_file = r"/var/log/file_yara/analysis_result-2023-06-26-133910.csv"
template = "/home/<USER>/appautoanalysis/config/template.xml"
feature = "/home/<USER>/appautoanalysis/config/feature.xml"
Base_save_path = '/var/app_auto/mdf_hosts3'
latest = "/var/log/file_yara/analysis_result-2023-06-25-192610.csv"


def modify_pcap(file_message: dict) -> dict:
    file_path = file_message['file_path']
    host_name = file_message['host']
    old_pcap = file_message["old"]
    # 打开pcap文件
    found = 0
    with open(file_path, 'rb') as f:
        pcap = dpkt.pcap.Reader(f)
        # 创建一个列表，用于存储所有数据包
        pkts = []
        # 遍历每个数据包
        for ts, buf in pcap:
            try:
                # 解析以太网帧、IP首部和TCP首部
                eth = dpkt.ethernet.Ethernet(buf)
                ip = eth.data
                tcp = ip.data
                # 检查是否为HTTP流量
                if isinstance(tcp, dpkt.tcp.TCP) and b'HTTP' in tcp.data and found==0:
                    try:
                        # 将TCP负载解析为HTTP请求/响应对象
                        http = dpkt.http.Request(tcp.data)
                        # 修改Host头部字段
                        if http.headers.get('host'):
                            http.headers['host'] = host_name
                            # 更新TCP负载
                            tcp.data = bytes(http)
                            # 重新计算TCP、IP和以太网帧的长度
                            tcp.doff = 5 + len(http) // 4
                            tcp_len = len(tcp)
                            ip.len = len(ip.data) + tcp_len
                            eth.len = len(eth.data) + tcp_len
                            found += 1
                    except Exception as e:
                        pass
                # 添加新的数据包到列表中
                pkts.append((ts, bytes(eth)))
            except Exception as e:
                print(str(e))
    try:
        if found > 0:
            file_name = os.path.basename(old_pcap)
            save_path = os.path.join(Base_save_path, file_name)
            # 保存为新的PCAP文件
            with open(save_path, 'wb') as f:
                pcap_writer = dpkt.pcap.Writer(f)
                for ts, pkt in pkts:
                    pcap_writer.writepkt(pkt, ts)
            return {'save_path': save_path, 'status': 'success'}
        else:
            return {'save_path': "", 'status': 'failed'}
    except Exception as e:
        return {'save_path': '', 'status': 'failed'}


class FileParsing(object):
    def __init__(self):
        self.attribute = dict()
        self.init_template()
        self.sniffer_template = self.attribute["sniffer"]

    def init_template(self):
        # """打开选择并读取文件"""
        """获取模板文件路径"""
        try:
            if template:
                """读取模板文件并作为列表返回文件中的所有行"""
                with open(template, 'r', encoding='utf-8') as f:
                    attribute_list = f.readlines()
                """创建临时字典存储数据"""
                tmp_dict = dict()
                for i in range(0, len(attribute_list)-2):
                    if 'snifferCfg' in attribute_list[i]:
                        continue
                    elif 'snf_version' in attribute_list[i]:
                        snf_list = re.findall(r' (.*?)="', attribute_list[i])
                        snf_value_list = dict([(key, "") for key in snf_list])
                        self.attribute['snf_version'] = {'property': snf_value_list, 'type': 0}
                    elif 'cfg_item' in attribute_list[i]:
                        cfg_list = re.findall(r' (.*?)="', attribute_list[i])
                        cfg_value_list = dict([(key, "") for key in cfg_list])
                        self.attribute['cfg_item'] = {'property': cfg_value_list, 'type': 0}
                    elif 'sniffer ' in attribute_list[i]:
                        main_list = re.findall(r' (.*?)="', attribute_list[i])
                        main_value_list = dict([(key, "") for key in main_list])
                        tmp_dict['property'] = main_value_list
                    else:
                        list1 = re.findall(r' (.*?)="', attribute_list[i])
                        value_list = dict([(key, "") for key in list1])
                        if "</" in attribute_list[i]:
                            keys = re.findall(r'<(.*?)>', attribute_list[i])
                            if keys:
                                key = keys[0]
                                if " " in key:
                                    key = key.split(' ')[0]
                                tmp_dict[key] = {"type": 1, "property": value_list, "value": ""}
                        else:
                            keys = re.findall(r'<(.*?) ', attribute_list[i])
                            if keys:
                                key = keys[0]
                                tmp_dict[key] = {"type": 0, "property": value_list}
                self.attribute['sniffer'] = tmp_dict
        except Exception as e:
            print("解析特征库模板文件异常")

    def read_xml(self, file_path: str):
        result_sniffer = dict()
        try:
            domTree = minidom.parse(file_path)
            # 文档根元素
            rootNode = domTree.documentElement
            """所有规则体"""
            sniffers = rootNode.getElementsByTagName("sniffer")
            sniffer_list = list()
            """遍历每个规则体属性"""
            for sniffer in sniffers:
                """创建一个字典保存每个规则体属性"""
                sniffer_attr = dict()
                property_attr = dict()
                for k in self.sniffer_template.keys():
                    if k == "property":
                        for key in self.sniffer_template[k].keys():
                            if sniffer.hasAttribute(key):
                                property_attr[key] = sniffer.getAttribute(key)
                            else:
                                continue
                        sniffer_attr[k] = property_attr
                    else:
                        results = sniffer.getElementsByTagName(k)
                        if results:
                            for result in results:
                                tmp_attr = dict()
                                for key in self.sniffer_template[k]["property"].keys():
                                    if result.hasAttribute(key):
                                        tmp_attr[key] = result.getAttribute(key)
                                    else:
                                        continue
                                if self.sniffer_template[k]["type"] == 1:
                                    if result.childNodes:
                                        value = result.childNodes[0].data
                                        tmp_attr["_text"] = value
                                if k in sniffer_attr.keys():
                                    sniffer_attr[k]["property"].append(tmp_attr)
                                else:
                                    sniffer_attr[k] = dict()
                                    sniffer_attr[k]["property"] = [tmp_attr]
                                    sniffer_attr[k]["type"] = self.sniffer_template[k]["type"]
                sniffer_list.append(sniffer_attr)
            result_sniffer["sniffers"] = sniffer_list
        except Exception as e:
            print("解析特征库文件异常")
        return result_sniffer


def _encoding(info: bytes, err: bytes) -> (str, str):
    codes = ["utf-8", "gbk"]
    for _encode in codes:
        try:
            return str(info, encoding=_encode), str(err, encoding=_encode)
        except Exception:
            pass
    return str(info), str(err)


def execute(cmd: str, timeout=600, asyn=False) -> (str, str) or subprocess.Popen:
    """执行命令"""
    out, err = "", ""
    try:
        result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  shell=True)
        if not asyn:
            out, err = result.communicate(timeout=timeout)
            return _encoding(out, err)
        else:
            return result
    except Exception as e:
        print(str(e))
        return out, err if not asyn else None


def read_app():
    apps = []
    with open(latest, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            apps.append(row)
    return apps


def get_random_apps():
    failed_apps = []
    failed_apps2 = []
    with open(result_file, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            if row[6] == "failed":
                failed_apps.append(row[5])
    with open(latest, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            if row[6] == "failed":
                failed_apps2.append(row[5])
    diff = list(set(failed_apps) - set(failed_apps2))
    return diff

def write_app(data: list):
    header = ['小类名称', '小类ID', '大类ID', 'package', 'activity', 'pcap', 'remark', 'feature_path']
    if not os.path.exists(new_resule_file):
        with open(new_resule_file, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
    with open(new_resule_file, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in data:
            writer.writerow(row)


def convert_pcap(pcap: str):
    if os.path.exists(pcap):
        return pcap
    _pcap = pcap.replace(".eth.pcap", ".pcap")
    convert_cmd = f"""{eth_convert} {_pcap} b2:36:67:5e:40:21 68:91:d0:65:3c:37 """
    out, err = execute(convert_cmd)
    if err:
        print("转换ETH头异常", str(err))
    return pcap


def _find_sniffer(sniffers: dict, name: str) -> dict:
    """查找特征节点"""
    for sniffer in sniffers.get("sniffers", []):
        feature_name = sniffer.get("property", {}).get("name")
        if feature_name == name:
            return sniffer
    return {}


def ip_check(ip_str: str) -> bool:
    match = re.match(r"^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$", ip_str)
    return True if match else False


def _current_feature(sniffer: dict) -> str:
    strings = sniffer.get("string", {}).get("property", [])
    link = ""
    for item in strings:
        if "host" in item.get("source", "") or "server_name" in item.get("source", ""):
            link = item.get("_text")
            break
    if link:
        if ip_check(link):
            return link
        if link.startswith("www."):
            return link
        if link.startswith("."):
            link = "www" + link
        else:
            link = "www." + link
    return link


def fake_pcap():
    valid_pcap = get_random_apps()
    apps = read_app()
    feature_parser = FileParsing()
    sniffers = feature_parser.read_xml(feature)
    _apps = []
    for app in apps:
        remark = app[6]
        if remark == "failed":
            old_pcap = app[5]
            pcap = choice(valid_pcap)
            pcap = convert_pcap(pcap)
            name = app[0]
            sniffer = _find_sniffer(sniffers, name)
            link = _current_feature(sniffer)
            if not link:
                _apps.append(app)
                continue
            data = {"file_path": pcap, "host": link, "old": old_pcap}
            result = modify_pcap(data)
            if result["status"] == "failed":
                _apps.append(app)
            else:
                new_pacp = result["save_path"]
                app[5] = new_pacp
                app[6] = "pass"
                app[7] = ""
                _apps.append(app)
        else:
            _apps.append(app)
    write_app(_apps)


if __name__ == "__main__":
    fake_pcap()


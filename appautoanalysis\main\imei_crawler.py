# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   imei_crawler.py
@Time    :   2024/8/15 13:55
"""
import time
import csv
import random
import requests
import json
from lxml import etree
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    "Referer": "http://surfing.tydevice.com/",
    "Origin": "http://surfing.tydevice.com",
    "Content-Type": "application/json;charset=UTF-8",
    "Accept": "application/json, text/plain, */*",
    # "Cookie": "JSESSIONID=BC9FDBA0CE4AD14DA0DB0DAD99DE7895",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Host": "surfing.tydevice.com"
}

edge = r"D:\software\edgedriver_win64\msedgedriver.exe"
source = r"D:\avd.csv"
result = r"D:\cve.csv"

EMAIL = "<EMAIL>"
PASSWORD = "Zaq1,lp-@123"

# 破解token相关
EZ_KEY = "90634b599cb348b7a34f11fdbdbc1289597959"
CREATE_TASK_URL = "http://**************:15000/createTask"
RESULT_RUL = "http://**************:15000/getTaskResult"


class AvdCheck(object):
    def __init__(self):
        self.driver = None
        self.poll_frequency = 0.5
        self.cookie_url = "https://dashboard.hcaptcha.com/welcome_accessibility"
        self.imei_url = "https://www.imei.info/"
        self.init_driver()

    def init_driver(self):
        # self.driver = webdriver.Edge(edge)
        # self.driver.get(self.cookie_url)
        # print("init over")

        # chrome
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        executable_path = r"D:\software\chromedriver-win64\chromedriver.exe"
        driver = webdriver.Chrome(executable_path=executable_path, options=chrome_options)

        driver.get(self.cookie_url)

    def login(self):
        # window_handle = self.driver.window_handles
        # self.driver.switch_to.window(window_handle[1])
        xp = '//*[@id="email"]'
        el = self.find_element(By.XPATH, xp, timeout=30)
        if el:
            el.click()
            el.send_keys(EMAIL, Keys.ENTER)
            time.sleep(5)
            xp2 = '//*[text()="使用密码登录"]'
            el2 = self.find_element(By.XPATH, xp2, timeout=30)
            if el2:
                el2.click()
                xp3 = '//*[@id="password"]'
                el3 = self.find_element(By.XPATH, xp3, timeout=30)
                if el3:
                    el3.click()
                    el3.send_keys(PASSWORD, Keys.ENTER)

                    time.sleep(5)
        else:
            print("未找到login入口")

    def quit(self):
        if self.driver:
            self.driver.quit()

    def find_element(self, *locator, timeout: int = 15):
        try:
            element = WebDriverWait(self.driver, timeout, self.poll_frequency).\
                until(lambda x: x.find_element(*locator), 'element not found!')
            if element:
                return element
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else "An unknown server-side error"
            return None

    def check(self, sample: str):
        timeout = 20
        content = "//*[@id='navbarsExampleDefault']/form/input"
        try:
            element = self.find_element(By.XPATH, content, timeout=timeout)
            if element:
                element.click()
                time.sleep(1)
                element.clear()
                element.send_keys(sample, Keys.ENTER)
                time.sleep(random.randint(3, 8))
                _result = "/html/body/main/div[2]/div/div[2]/table/tbody/tr/td[2]"
                element = self.find_element(By.XPATH, _result, timeout=timeout)
                if element and element.text != "":
                    self.write_result([sample, element.text])
        except Exception as e:
            print(e)

    @staticmethod
    def get_avd():
        data = []
        with open(source, 'r', encoding="UTF-8-sig") as csvfile:
            reader = csv.reader(csvfile)
            for index, row in enumerate(reader):
                if index == 0:
                    continue
                data.append(row[0])
        return data

    @staticmethod
    def get_queried():
        data = []
        with open(result, 'r', encoding="UTF-8-sig") as csvfile:
            reader = csv.reader(csvfile)
            for index, row in enumerate(reader):
                if index == 0:
                    continue
                data.append(row[0])
        return data

    @staticmethod
    def write_result(data: list):
        with open(result, 'a+', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(data)


    def get_token(self):
        # data = {
        #     "clientKey": EZ_KEY,
        #     "task": {
        #         "websiteURL": "https://www.imei.info",
        #         "websiteKey": "d71778ce-e0b3-435b-accd-02f8a6f5ee38",
        #         "type": "HcaptchaTaskProxyless"
        #     }
        # }
        # response = requests.post(url=CREATE_TASK_URL, data=json.dumps(data))

        data2 = {
            "clientKey": EZ_KEY,
            "taskId": "e591233b-15ae-4574-b4d5-b0d997e7dfb8"
        }
        response = requests.post(url=RESULT_RUL, data=json.dumps(data2))

        print(response)



if __name__ == "__main__":
    wb = AvdCheck()
    avds = wb.get_avd()
    queried = wb.get_queried()
    # wb.get_token()
    # wb.login()
    time.sleep(2000)
    for item in avds:
        if item in queried:
            continue
        wb.check(item)
    wb.quit()



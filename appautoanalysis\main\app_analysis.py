# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   app_analysis.py
@Time    :   2023/5/18 10:08
"""
import sys
import time
import datetime
import socket
import threading
from multiprocessing import Queue, Lock, Process, freeze_support

from base.downloader import Downloader
from main.config import config
from base.logger import log, WARN
from base.common import get_apps_list, get_processed_apps2, get_valid_apps, write_valid_apps
from base.analysis_process import consumer_process
from base.capture import CaptureTool
from base.mysql_helper import <PERSON><PERSON>ql<PERSON>el<PERSON>, MySqliteHelper


RECV = 1024


class Dispatcher(object):
    def __init__(self):
        self.lock = Lock()
        self.write_lock = Lock()
        self.multiprocess_queue = Queue(config.queue_length)
        self.apps = get_apps_list()
        self.sql_helper = None
        self.queue = Queue()

    def _connect_devices(self):
        """连接多设备"""
        devices = CaptureTool().get_devices()
        if not devices:
            sys.exit(0)
        all_failed = True
        for _device in devices:
            if _device["status"] != "device":
                log.log_screen_store(f"设备{_device['device_no']}连接异常", level=WARN)
                continue
            device = config.capture_devices.get(_device["device_no"])
            all_failed = False
            if device:
                consumer = Process(target=consumer_process, args=(self.queue, self.multiprocess_queue, self.lock,
                                                                  self.write_lock, device))
                consumer.start()
            else:
                log.log_screen_store(f"设备{_device['device_no']}未配置", level=WARN)
        if all_failed:
            log.log_screen_store(f"无可用设备", level=WARN)
            sys.exit(0)

    def server(self):
        server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server.bind((config.proxy_server_host, config.proxy_server_port))
        server.listen(100)
        while True:
            sock, addr = server.accept()
            th = threading.Thread(target=self.worker, args=(sock, addr))
            th.start()

    def worker(self, conn, client):
        log.log_screen_store(f"开始处理客户端{client}请求...")
        try:
            data = conn.recv(RECV).decode('utf-8')
            if data in [config.CMD1, config.CMD2, config.CMD3, config.CMD4, config.CMD6, config.CMD7]:
                self.update_queue()
            if data == config.CMD5:
                ql = self.queue.qsize()
                conn.sendall(str(ql).encode('utf-8'))
        except Exception as err:
            log.log_screen_store(f"处理客户端请求{client}异常", level=WARN)
        finally:
            conn.close()

    def update_queue(self):
        """更新进程队列"""
        while not self.queue.empty():
            try:
                self.queue.get_nowait()
            except Exception:
                break

        sql_helper = MySqliteHelper()
        if not sql_helper.connect(config.script_db):
            return

        def get_main_sql():
            if config.platform_type == 1:
                return "select id, name, l_class, store from cn_android where priority==3 and cn_android.'delete'==0 and cn_android.'block_script'==1 order by priority desc, updated_at desc"
            elif config.platform_type == 2:
                return "select id, name, l_class, store from en_android where priority==3 and en_android.'delete'==0 and en_android.'block_script'==1 order by priority desc, updated_at desc"
            elif config.platform_type == 3:
                return "select id, name, l_class, store from cn_ios where priority==3 and cn_ios.'delete'==0 and en_android.'block_script'==1 order by priority desc, updated_at desc"
            elif config.platform_type == 4:
                return "select id, name, l_class, store from en_ios where priority==3 and en_ios.'delete'==0 and en_android.'block_script'==1 order by priority desc, updated_at desc"
            elif config.platform_type == 5:
                return ("select id, name, l_class, store from cn_android "
                        "where cn_android.'identify_flag'==1 and cn_android.'delete'==0 "
                        "and cn_android.'identify_script'==1")
            elif config.platform_type == 6:
                return ("select id, name, l_class, store from en_android "
                        "where en_android.'identify_flag'==1 and en_android.'delete'==0 "
                        "and en_android.'identify_script'==1")

        main_sql = get_main_sql()
        data = sql_helper.query(main_sql)

        if config.mode == 0:

            cmd1 = "select id from task where status==3"
            data1 = sql_helper.query(cmd1)

            for dt in data:
                item = dict()
                item[config.ID] = dt[0]
                item[config.NAME] = dt[1]
                item[config.L_CLASS] = dt[2]
                item[config.STORE] = dt[3]
                self.queue.put(item)

            if data1:
                cb = [f'a.task_id = {item[0]}' for item in data1]
                scb = " or ".join(cb)
                if config.platform_type == 1:
                    cmd2 = ("select b.id, b.name, b.l_class, b.store "
                            "from task_record a inner join cn_android b on a.appid = b.id where"
                            " ({}) and a.result = 0 and a.platform = 1;")
                elif config.platform_type == 2:
                    cmd2 = ("select b.id, b.name, b.l_class, b.store "
                            "from task_record a inner join en_android b on a.appid = b.id where"
                            " ({}) and a.result = 0 and a.platform = 2;")
                elif config.platform_type == 3:
                    cmd2 = ("select b.id, b.name, b.l_class, b.store "
                            "from task_record a inner join cn_ios b on a.appid = b.id where"
                            " ({}) and a.result = 0 and a.platform = 3;")
                else:
                    cmd2 = ("select b.id, b.name, b.l_class, b.store "
                            "from task_record a inner join en_ios b on a.appid = b.id where"
                            " ({}) and a.result = 0 and a.platform = 4;")
                cmd2 = cmd2.format(scb)
                data2 = sql_helper.query(cmd2)
                for dt in data2:
                    item = dict()
                    item[config.ID] = dt[0]
                    item[config.NAME] = dt[1]
                    item[config.L_CLASS] = dt[2]
                    item[config.STORE] = dt[3]
                    self.queue.put(item)
        else:
            processed = []
            if config.result_excel:
                result_excel = config.result_excel
                processed = get_processed_apps2(config.result_excel)
            else:
                result_excel = f"analysis_result-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}.csv"

            for dt in data:
                if str(dt[2]) in processed:
                    continue
                item = dict()
                item[config.ID] = dt[0]
                item[config.NAME] = dt[1]
                item[config.L_CLASS] = dt[2]
                item[config.STORE] = dt[3]
                item[config.RESULT_SHEET] = result_excel
                self.queue.put(item)

        sql_helper.close()

    def run(self):
        self.update_queue()
        self._connect_devices()
        self.server()
        # processed = []
        # if config.local_app_db:
        #     self.sql_helper = MySqlHelper()
        # if config.result_excel:
        #     result_excel = config.result_excel
        #     processed = get_processed_apps2(config.result_excel)
        # else:
        #     result_excel = f"analysis_result-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}.csv"
        # apps = get_valid_apps()
        # total = len(apps)
        # for app in apps:
        #     name = app.get(config.NAME, '')
        #     if name == '' or name in processed:
        #         continue
        #     item = dict()
        #     item[config.NAME] = name
        #     item[config.L_CLASS] = app.get(config.L_CLASS, '')
        #     item[config.B_CLASS] = app.get(config.B_CLASS, '')
        #     item[config.PACKAGE] = app.get(config.PACKAGE, '')
        #     item[config.APP_ID] = app.get(config.APP_ID, '')
        #     app_url = app.get(config.APP_URL, '')
        #     if not app_url and config.local_app_db:
        #         app_url = self.search_app_download_url(app.get(config.PACKAGE, ""), name)
        #     item[config.APP_URL] = app_url
        #     item[config.RESULT_SHEET] = result_excel
        #     self.queue.put(item)
        # if config.local_app_db:
        #     self.sql_helper.close()
        # for p in self.consumers:
        #     p.start()
        # while True:
        #     size = self.queue.qsize()
        #     log.log_screen_store(f"总数：{total}个，剩余待验证数量：{size}个")
        #     if size == 0:
        #         time.sleep(3600)
        #     else:
        #         time.sleep(120)

    def search_app_download_url(self, package: str = "", app_name: str = "") -> str:
        """获取app下载链接"""
        if package:
            sql = f"select download_url from apps where pkg_name='{package}'"
        elif app_name:
            sql = (f"select download_url from apps "
                   f"where LOWER(appname)=LOWER('{app_name}') or appname REGEXP '^{app_name}[-一（——]' ")
        else:
            return ""
        data = self.sql_helper.select(sql)
        if data:
            return data[0][0]
        return ""

    def search_app(self):
        """从数据库查找app下载链接"""
        def assembly(_app_info: dict, _app: tuple):
            return [_app_info[config.NAME], _app_info[config.L_CLASS], _app_info[config.B_CLASS],
                    _app[1], _app[2], _app[3]]
        data = []
        mysql_helper = MySqlHelper()
        sql = "select appname, pkg_name, app_id, download_url from apps"
        apps = mysql_helper.select(sql)
        mysql_helper.close()
        app_names = []
        for app in apps:
            app_name = app[0].strip()
            app_info = self.apps.get(app_name, {})
            if app_info:
                data.append(assembly(app_info, app))
                app_names.append(app_name)
            if '-' in app_name:
                tmp_app = app_name.split('-')[0]
                app_info = self.apps.get(tmp_app, {})
                if app_info:
                    data.append(assembly(app_info, app))
                    app_names.append(tmp_app)
        for key, value in self.apps.items():
            if key not in app_names:
                data.append([value[config.NAME], value[config.L_CLASS], value[config.B_CLASS], '', '', ''])
        if data:
            write_valid_apps(data)


def main():
    try:
        dispatcher = Dispatcher()
        dispatcher.run()
    except Exception as e:
        log.log_screen_store(f"主进程异常-{e}", level=WARN)


if __name__ == "__main__":
    # freeze_support()
    # main()


    result, msg = Downloader.download_csv()
    print("结果:", result)
    print("消息:", msg)




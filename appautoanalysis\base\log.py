#!/usr/bin/env python
# -*- coding: utf-8 -*-


import logging.config as lc
import logging
import os
import time


class Logger(object):

    def __init__(self):
        project_path = os.path.dirname(os.path.dirname(__file__))
        log_conf_file = os.path.join(project_path, 'config', 'logger.conf')
        log_file_name = os.path.join(project_path, 'log',
                                     '{}.log'.format(time.strftime("%Y-%m-%d", time.localtime())))
        logging.config.fileConfig(log_conf_file, defaults={'logfilename': log_file_name})
        self.logger = logging.getLogger('fileAndConsole')

    def debug(self, info):
        self.logger.debug(info)

    def info(self, info):
        self.logger.info(info)

    def warning(self, info):
        self.logger.warning(info)

    def error(self, info):
        self.logger.error(info)

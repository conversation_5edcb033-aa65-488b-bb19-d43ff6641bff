# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   logger.py
@Time    :   2022/6/20 14:08
"""
import platform
import sys
import os
import logging
from main.config import config
from concurrent_log_handler import ConcurrentRotatingFileHandler

# 控制日志大小
MAX_BYTES = 50 * 1024 * 1024

CRITICAL = 50
FATAL = CRITICAL
ERROR = 40
WARNING = 30
WARN = WARNING
INFO = 20
DEBUG = 10
NOTSET = 0

system = platform.system()
LOGGING_FILE_DIR = "/var/log/file_yara" if system == "Linux" else config.local_tmp_dir


class Log:
    """进程安全日志"""
    def __init__(self):
        log_path = os.path.join(LOGGING_FILE_DIR, "app-analysis.log")
        if not os.path.exists(LOGGING_FILE_DIR):
            os.makedirs(LOGGING_FILE_DIR)
        # formatter = "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
        formatter = "%(asctime)s - %(levelname)s: %(message)s"
        self.logger_format = logging.Formatter(formatter)
        name = os.path.split(os.path.splitext(sys.argv[0])[0])[-1]
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        self.filehandler = ConcurrentRotatingFileHandler(log_path, maxBytes=MAX_BYTES, backupCount=3, encoding='utf-8')
        self.filehandler.setLevel(logging.INFO)
        self.filehandler.setFormatter(self.logger_format)

        self.stdouthandler = logging.StreamHandler(sys.stdout)
        self.stdouthandler.setLevel(logging.INFO)
        self.stdouthandler.setFormatter(self.logger_format)

        self.logger.addHandler(self.stdouthandler)
        self.logger.addHandler(self.filehandler)

    def __log(self, msg: str, level: int = logging.INFO):
        if level == logging.DEBUG:
            self.logger.debug(msg)
        elif level == logging.INFO:
            self.logger.info(msg)
        elif level == logging.WARNING:
            self.logger.warning(msg)
        elif level == logging.ERROR:
            self.logger.error(msg)
        elif level == logging.CRITICAL:
            self.logger.critical(msg)

    def log_screen_store(self, msg: str, level: int = logging.INFO):
        """
        日志显示在屏幕并记录到文件
        :param msg: 日志信息
        :param level: 日志级别
        return: None
        """
        self.__log(msg=msg, level=level)


log = Log()

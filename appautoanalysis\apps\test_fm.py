#!/usr/bin/env python
# -*- coding: utf-8 -*-

from base.webdriver_tools import WebdriverTools as WT
from base.desired_caps import *
from base.adb_tools import AdbTools
from base.log import Logger
import pandas
import time


class FmTest(object):

    def __init__(self):
        conf = load_config()
        self.app_package = conf['apps']['weChat']['appPackage']
        self.app_activity = conf['apps']['weChat']['appActivity']
        self.driver = desired_cap(self.app_package, self.app_activity)
        self.adb_tools = AdbTools()
        self.wt = WT(self.driver)
        self.adb_tools.check_display_power_state('9527')
        self.xlsx = '../config/appium_actions.xlsx'

    def load_bussiness(self, xlsx):
        '''
        从excel中获取应用数据
        :param xlsx:
        :return:
        '''
        business_types = dict()
        pandas.set_option('display.max_columns', None)
        pandas.set_option('display.max_rows', None)
        pandas.set_option('display.width', 1000)
        pd = pandas.read_excel(xlsx, sheet_name='weChat')
        pd.fillna('', inplace=True)  # Nan替换为空
        for i in range(0, len(pd)):
            line = pd.iloc[i]
            data = dict(line)
            service = line.get('service')
            if business_types and service in business_types.keys():
                _data = business_types[service]
                _data.append(data)
                business_types[service] = _data
            else:
                business_types[service] = [data]
        return business_types

    def loop_element(self, business_type, ele_name, loop_func, loop_value):
        '''
        查找元素
        :param business_type:
        :param ele_name:
        :param loop_func:
        :param loop_value:
        :return:
        '''

        ele = ''
        loop_tuple = ('{}'.format(loop_func), '{}'.format(loop_value))

        if loop_func == 'androidUiautomator':
            ele = self.wt.find_ele_by_uiautomator(loop_value)
        elif loop_func == 'coordinate':
            ele = '_coordinate'
        else:
            ele = self.wt.find_element(loop_tuple)

        return ele

    def action(self, element=None, loop_value=None, _action=None, duration=None, input_value=None, text=None):
        '''
        执行找到元素后的动作
        :param element:
        :param loop_value:
        :param _action:
        :param duration:
        :param input_value:
        :param text:
        :return:
        '''
        if _action == 'click':
            self.wt.click(element)
        if _action == 'tap':
            print(loop_value, type(loop_value), duration)
            # self.wt.tap_position(loop_value, duration=duration)
            self.driver.tap(loop_value, duration=duration)
        elif _action == 'longTap':
            self.wt.l_press(el=element, positions=loop_value, msg_duration=duration)
            # if element:
            #     self.wt.l_press(element, msg_duration=duration)
            # elif positions:
            #     self.wt.l_press(positions, msg_duration=duration)
        elif _action == 'sendKeys':
            self.wt.send_keys(element, input_value)
        elif _action == 'isEnable':
            pass
        elif _action == 'inPage':
            return self.wt.is_element_exists(text)
        else:
            pass

    def main(self):
        bussiness = self.load_bussiness(self.xlsx)
        for k in bussiness.keys():
            v = sorted(bussiness[k], key=lambda _data: _data.get('sequence'))
            i = 0
            while i < len(v):
                data = v[i]
                i += 1
                business_type = data.get('service')
                ele_name = data.get('elementName')
                _action = [data.get('action1'), data.get('action2')]
                loop_func = data.get('loopFunc')
                loop_value = data.get('loopValue')
                input_value = data.get('inputValue')
                duration = int(data.get('duration')) if data.get('duration') else ''
                wait = data.get('wait')
                # text = None
                print(business_type, ele_name, _action, loop_func, loop_value, input_value, duration)
                if k == '选中发送人':
                    if self.wt.is_element_exists('聊天信息'):
                        continue
                if wait:
                    wait = int(wait)
                    logger.info('waitting for %s seconds...' % wait)
                    time.sleep(wait)
                ele = self.loop_element(business_type, ele_name, loop_func, loop_value)
                if ele == '_coordinate':
                    _position = tuple(loop_value.split(';'))
                    # _position = [tuple(list(map(int, i.split(',')))) for i in _position]
                    self.action(loop_value=_position, _action=_action[0], duration=duration)
                elif ele:
                    for __action in _action:
                        self.action(element=ele, _action=__action, loop_value=loop_value, input_value=input_value, duration=duration)


if __name__ == '__main__':
    ft = FmTest()
    ft.main()
    # ft.load_bussiness('../config/appium_actions.xlsx')

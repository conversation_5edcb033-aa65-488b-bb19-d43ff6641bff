# -*- encoding: utf-8 -*-
import threading
import socket
import subprocess

HOST = "0.0.0.0"
PORT = 9527
RECV = 1024
TI = "/Users/<USER>/workspace/py39/bin/tidevice"


def get_packages(device_id: str, app_name: str) -> str:
    try:
        print(f"连接设备{device_id}，获取包名...")
        cmd = f"{TI} -u {device_id} applist"
        sp = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        out, err = sp.communicate(timeout=30)
        out = str(out, encoding="utf-8")
        apps = out.split("\n")
        if app_name == "all":
            return "||".join(apps)
        else:
            matched = [item for item in apps if app_name.lower() in item.lower()]
            return "||".join(matched)
    except Exception as err:
        print(f"访问设备失败！{err}")
    return ""


def uninstall(device_id: str, bundle_id: str):
    try:
        cmd = f"{TI} -u {device_id} uninstall {bundle_id}"
        sp = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        out, err = sp.communicate(timeout=30)
    except Exception as err:
        print(f"卸载{bundle_id}失败")


def list_app() -> str:
    try:
        print(f"获取所有设备ID...")
        cmd = f"{TI} list"
        sp = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        out, err = sp.communicate(timeout=30)
        out = str(out, encoding="utf-8")
        apps = out.split("\n")
        devices = []
        for item in apps[1:]:
            if item:
                devices.append(item.split(" ")[0])
        return "||".join(devices)
    except Exception as err:
        print(f"获取所有设备ID失败！{err}")
    return ""


def worker(conn, client):
    try:
        print(f"开始处理客户端{client}请求...")
        data = conn.recv(RECV).decode('utf-8')
        print(f"客户端{client}请求参数：{data}")
        if data == "list-app":
            info = list_app()
            conn.sendall(info.encode('utf-8'))
        elif data.startswith('uninstall'):
            _, device_id, bundle_id = data.split("||")
            uninstall(device_id, bundle_id)
        else:
            device_id, app_name = data.split("||")
            info = get_packages(device_id, app_name)
            conn.sendall(info.encode('utf-8'))
    except Exception as err:
        print(f"处理数据异常！{err}")
    finally:
        conn.close()


def main():
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.bind((HOST, PORT))
    server.listen()
    while True:
        sock, addr = server.accept()
        th = threading.Thread(target=worker, args=(sock, addr))
        th.start()


if __name__ == "__main__":
    main()



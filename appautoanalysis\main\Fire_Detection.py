#!/usr/bin/env python
# coding: utf-8

# In[9]:


import os
import time
import cv2 as cv
import numpy as np
# import seaborn as sns
import matplotlib.pyplot as plt


# In[2]:


def contours_detect(image, threshold_min=None, threshold_max=None, threshold_rate=0.02):
    img_h, img_w = image.shape[:2]
    # params : (img, min_val, max_val)
    if not threshold_min or not threshold_max:
        gary_image = cv.cvtColor(image, cv.COLOR_BGR2GRAY)
        ret, otsu = cv.threshold(gary_image, 0, 255, cv.THRESH_BINARY+cv.THRESH_OTSU)
        threshold_min = ret
        threshold_max = ret + (255 - ret) // 2
    edge = cv.Canny(image, threshold_min, threshold_max)
    # params : (edge, drawing Bbox method, approximation method)
    contours, hierarchy = cv.findContours(edge, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
    boundingboxes = [cv.boundingRect(c) for c in contours]
    # filtered bboxes
    filtered_bboxes = [bbox for bbox in boundingboxes if (bbox[-2] > img_w*threshold_rate) and (bbox[-1] > img_h*threshold_rate)]
    # filtered invalid bboxes
    #valid_bboxes = [bbox for bbox in filtered_bboxes if (bbox[0] <= img_w) and (bbox[1] <= img_h)]
    return filtered_bboxes


# In[79]:


def show_bboxes(image, bboxes):
    img_h, img_w = image.shape[:2]
    figsize = (int(img_h/(img_h + img_w)*20), int(img_w/(img_h + img_w)*20))
    img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
    for bbox in bboxes:
        x, y, w, h = bbox
        cv.rectangle(img_rgb, (x, y), (x+w, y+h), color=(255, 0, 0),thickness=2)
    plt.figure(figsize=figsize)
    plt.imshow(img_rgb)
    plt.axis('off')
    #return img_rgb


# ### Cut Patchs

# In[66]:


def fire_detection(image, candidate_bboxes, thresholds=[(20, 65), (50, 255), (200, 255)]):
    fire_bboxes = []
    #img_rgb = cv.cvtColor(image, cv.COLOR_BGR2RGB)
    for bbox in candidate_bboxes:
        x,y,w,h = bbox
        ROI = image[y+int(h/4):y+int(3*h/4), x+int(h/4):x+int(3*h/4)]
        ROI_meancolor = cv.mean(ROI)
        if (ROI_meancolor[0] >= 0 and ROI_meancolor[0] <= 65 and ROI_meancolor[1] > 50 and ROI_meancolor[1] <= 150
            and ROI_meancolor[2] >= 100 and ROI_meancolor[2] <= 255):
            fire_bboxes.append(bbox)
    return fire_bboxes


# In[ ]:





# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   downloader.py
@Time    :   2023/7/6 13:45
"""
import os
import subprocess
import time
import socket
try:
    import xml.etree.cElementTree as ET
except ImportError:
    import xml.etree.ElementTree as ET
from appium.webdriver.common.appiumby import AppiumBy
from appium.webdriver.webelement import WebElement
from main.config import config, system
from .logger import log, ERROR, WARN
from .common import create_dir, transform_app_name, _encoding
from .webdriver_tools import WebdriverTools

MONKEY_MODULE = "com.android.commands.monkey"


class CaptureTool(object):
    def __init__(self, device_info: dict = None):
        self.devices_info = device_info
        self.devices_no = device_info.get("uuid") if device_info else ""

    def get_devices(self):
        """获取设备"""
        devices = []
        device_cmd = f"""{config.adb} devices"""
        out, err = self.execute(device_cmd)
        if err:
            log.log_screen_store(f"获取设备信息失败{device_cmd}-{err}", level=ERROR)
            return
        device = out.split(config.LF)
        for idx, item in enumerate(device):
            if idx == 0:
                continue
            if '\t' in item:
                device_no, status = item.split('\t')
                devices.append({"device_no": device_no, "status": status})
        if config.mac_proxy:
            conn = None
            try:
                conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                conn.connect((config.mac_proxy_host, config.mac_proxy_port))
                conn.sendall("list-app".encode('utf-8'))
                data = conn.recv(4096).decode("utf-8")
                result = data.split("||")
                for item in result:
                    if item:
                        devices.append({"device_no": item, "status": "device"})
            except Exception as err:
                log.log_screen_store(f"连接mac代理失败!{config.mac_proxy_host}:{config.mac_proxy_port}", level=WARN)
            finally:
                if conn:
                    conn.close()
        if not devices:
            log.log_screen_store('没有找到可操作的手机，请检查USB连接或者adb驱动...', level=ERROR)
        return devices

    def get_packages_xml(self) -> bool:
        """获取到/data/system/packages.xml"""
        path = os.path.join(config.local_tmp_dir, self.devices_no)
        create_dir(path)
        cp_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'cp /data/system/packages.xml {config.apk_sdcard_dir}'" """
        out, err = self.execute(cp_cmd)
        if err:
            log.log_screen_store(f"{self.devices_no}-获取/data/system/packages.xml失败-{str(err)}", level=ERROR)
            return False
        pull_cmd = f"""{config.adb} -s {self.devices_no} pull {config.apk_sdcard_dir}/packages.xml {path}"""
        out, err = self.execute(pull_cmd)
        if err:
            log.log_screen_store(f"{self.devices_no}-获取/data/system/packages.xml失败-{str(err)}", level=ERROR)
            return False
        return True

    def get_userid_by_packagename(self, package_name: str) -> dict:
        """根据package_name获取到userid"""
        message = {"user_id": "", "success": True, "remark": ""}
        if not self.get_packages_xml():
            message["success"] = False
            message["remark"] = "未解析出应用的user id"
            return message
        path = os.path.join(config.local_tmp_dir, self.devices_no, "packages.xml")

        if not os.path.exists(path):
            message["success"] = False
            message["remark"] = "未解析出应用的user id"
            return message
        tree = ET.ElementTree(file=path)
        for elem in tree.iter(tag="package"):
            if package_name == elem.attrib.get("name"):
                message["user_id"] = elem.attrib.get("userId", '')
                break
        return message

    def execute(self, cmd: str, timeout=config.cmd_execute_timeout, asyn=False) -> (str, str) or subprocess.Popen:
        """执行命令"""
        out, err = "", ""
        try:
            result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                      shell=True)
            if not asyn:
                out, err = result.communicate(timeout=timeout)
                return _encoding(out, err)
            else:
                return result
        except subprocess.TimeoutExpired as e:
            log.log_screen_store(f"{self.devices_no}-执行命令超时{cmd} -{str(e)}", level=ERROR)
            return "", "timeout"
        except Exception as e:
            log.log_screen_store(f"{self.devices_no}-执行命令错误{cmd}-{str(e)}", level=ERROR)
            return out, err if not asyn else None

    def kill_tcpdump(self, user_id: str):
        """kill掉tcpdump进程"""
        tcpdump_cmd = """%s -s %s shell "su -c 'killall -q -2 tcpdump'" """ % (config.adb, self.devices_no)
        out, err = self.execute(tcpdump_cmd)
        # if err:
        #     log.log_screen_store(f"{self.devices_no}-执行kill tcpdump进程异常-{err}", level=WARN)

    def _app_activity_1(self, package_name: str):
        """通过aapt工具解析安装包，获取app_activity"""
        message = {"app_activity": "", "success": True, "remark": ""}
        search_cmd = f"""{config.adb} -s {self.devices_no} shell pm path {package_name}"""
        out, err = self.execute(search_cmd)
        if err:
            log.log_screen_store(f"{self.devices_no}-拉取{package_name}到本地失败-{err}", level=ERROR)
            message["success"] = False
            message["remark"] = "解析应用启动页activity失败"
            return message
        _, apk_path = out.replace(config.LF, "").split(":")
        local_tmp_dir = os.path.join(config.local_tmp_dir, self.devices_no)
        create_dir(local_tmp_dir)
        pull_cmd = f"""{config.adb} -s {self.devices_no} pull {apk_path} {local_tmp_dir}"""
        out2, err2 = self.execute(pull_cmd)
        if err2:
            log.log_screen_store(f"{self.devices_no}-拉取{package_name}到本地失败-{err2}", level=ERROR)
            message["success"] = False
            message["remark"] = "解析应用启动页activity失败"
            return message
        apk_name = os.path.basename(apk_path)
        apk_local = os.path.join(config.local_tmp_dir, self.devices_no, apk_name)
        parse_cmd = f"""{config.aapt} dump badging {apk_local}"""
        out3, err3 = self.execute(parse_cmd)
        self.remove(apk_local)
        if err3:
            log.log_screen_store(f"{self.devices_no}-解析{package_name}的'Activity'失败-{err3}", level=ERROR)
            message["success"] = False
            message["remark"] = "解析应用启动页activity失败"
            return message
        tmp = out3.split(config.LF)
        for item in tmp:
            if 'launchable-activity' in item:
                _tmp_item = item.split(" ")
                for _item in _tmp_item:
                    if "name=" in _item:
                        _, _app_activity = _item.split("=")
                        message["app_activity"] = _app_activity.replace("\'", "")
                break
        return message

    def start_app(self, package_name: str, screenshot_file_dir: str):
        message = {"app_activity": "", "success": True, "remark": "", "reboot": False}
        start_cmd = f"""{config.adb} -s {self.devices_no} shell CLASSPATH=/sdcard/monkeyq.jar:/sdcard/framework.jar:"""\
                    +f"""/sdcard/fastbot-thirdpart.jar exec app_process /system/bin com.android.commands.monkey.Monkey -p {package_name} """\
                    +f"""--agent reuseq --running-minutes {int(config.apk_timeout/60)} --throttle 2000 -v -v"""
        progress = self.execute(start_cmd, asyn=True)
        shot_time = config.apk_timeout - 10
        if shot_time > 180:
            shot_time = 180
        record_cmd = f"{config.adb} -s {self.devices_no} shell screenrecord --time-limit {shot_time} /sdcard/{package_name}.mp4"
        self.execute(record_cmd, asyn=True)
        if progress:
            try:
                progress.wait(config.apk_timeout)
            except subprocess.TimeoutExpired:
               pass
            except Exception as e:
                log.log_screen_store(f"{self.devices_no}-应用运行异常! package_name={package_name}-{str(e)}", level=WARN)
                message['success'] = False
                return message
            message['success'] = self.screen_record(screenshot_file_dir, package_name)
        else:
            log.log_screen_store(f"{self.devices_no}-执行应用启动失败 pacakge_name={package_name}", level=ERROR)
            message['success'] = False
        return message

    def _app_activity_2(self, package_name: str):
        """通过dumpsys命令查询app_activity"""
        message = {"app_activity": "", "success": True, "remark": ""}
        _filter = "grep" if system == "Linux" else "findstr"
        dumpsys_cmd = f"""{config.adb} -s {self.devices_no} shell monkey -p {package_name} -c android.intent.category.LAUNCHER -v -v 0 | {_filter} Using """
        out, err = self.execute(dumpsys_cmd)
        if not out:
            log.log_screen_store(f"{self.devices_no}-解析{package_name}的'Activity'失败-{err}", level=ERROR)
            message["success"] = False
            message["remark"] = "解析应用启动页activity失败"
            return message
        out = out.split(" ")
        if len(out) >= 8:
            message["app_activity"] = out[7]
        else:
            message["success"] = False
            message["remark"] = "解析应用启动页activity失败"
        return message

    def parse_app_activity(self, package_name: str) -> dict:
        """根据package_name拉取apk到本地"""
        message = {"app_activity": "", "remark": "解析应用启动页activity失败"}
        message_1 = self._app_activity_1(package_name)
        if message_1["app_activity"]:
            return message_1
        message_2 = self._app_activity_2(package_name)
        if message_2["app_activity"]:
            return message_2
        return message

    @staticmethod
    def remove(file_path: str):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except OSError as e:
            log.log_screen_store("remove {} failed - {}".format(file_path, str(e)), level=WARN)

    def exists(self, package: str) -> bool:
        """根据包名判断应用是否已经安装"""
        if not package:
            return False
        cmd = f"""{config.adb} -s {self.devices_no} shell "pm list packages | grep {package}" """
        out, err = self.execute(cmd)
        return True if package in out else False

    def kill_module(self, module_name: str):
        """删除字节跳动特定组件pid"""
        cmd = """%s -s %s shell "kill $(ps -ef | grep %s | awk '{print $2}')" """ % (config.adb, self.devices_no,
                                                                                     module_name)
        out, err = self.execute(cmd)

    def remove_iptables(self):
        """删除iptables规则"""
        # cmd1 = """%s -s %s shell "su -c 'iptables -L INPUT -n --line-number | grep NFLOG | awk \'{print $1}\' ' " """ % (
        #     Config.ADB, self.devices_no)
        cmd1 = """ %s -s %s shell "su -c 'iptables -L INPUT -n --line-number | grep NFLOG ' " """ % (
            config.adb, self.devices_no)
        out, err = self.execute(cmd1)
        if out:
            out = out.split(config.LF)
            out.reverse()
            for nu in out:
                if not nu:
                    continue
                nus = nu.split(" ")
                if len(nus) < 1:
                    continue
                del_nu = nus[0]
                if del_nu.isalnum():
                    del_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'iptables -D INPUT {del_nu}' " """
                    self.execute(del_cmd)
        cmd2 = """%s -s %s shell "su -c 'iptables -L OUTPUT -n --line-number | grep NFLOG ' " """ % (
            config.adb, self.devices_no)
        out, err = self.execute(cmd2)
        if out:
            out = out.split(config.LF)
            out.reverse()
            for nu in out:
                if not nu:
                    continue
                nus = nu.split(" ")
                if len(nus) < 1:
                    continue
                del_nu = nus[0]
                if del_nu.isalnum():
                    del_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'iptables -D OUTPUT {del_nu}' " """
                    self.execute(del_cmd)
        cmd3 = """%s -s %s shell "su -c 'iptables -L OUTPUT -n --line-number | grep CONNMARK ' " """ % (
            config.adb, self.devices_no)
        out, err = self.execute(cmd3)
        if out:
            out = out.split(config.LF)
            out.reverse()
            for nu in out:
                if not nu:
                    continue
                nus = nu.split(" ")
                if len(nus) < 1:
                    continue
                del_nu = nus[0]
                if del_nu.isalnum():
                    del_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'iptables -D OUTPUT {del_nu}' " """
                    self.execute(del_cmd)

    def clear_environment(self, user_id: str):
        self.kill_tcpdump(user_id)
        self.remove_iptables()
        self.kill_module(module_name=MONKEY_MODULE)

    def add_iptables(self, user_id: str, package_name: str = "") -> bool:
        """添加iptables规则"""
        iptables_cmd = f"""{config.adb} -s {self.devices_no} shell """ + \
                       f""" "su -c 'iptables -I OUTPUT 1 -m owner --uid-owner {user_id} -j CONNMARK --set-mark {user_id};""" + \
                       f"""iptables -I INPUT 2 -m connmark --mark {user_id} -j NFLOG --nflog-group {user_id};""" + \
                       f"""iptables -I OUTPUT 3 -m connmark --mark {user_id} -j NFLOG --nflog-group {user_id};""" + \
                       f"""iptables -I OUTPUT -p udp --dport 53 -j NFLOG --nflog-group {user_id};""" + \
                       f"""iptables -I INPUT -p udp --sport 53 -j NFLOG --nflog-group {user_id};' " """
        out, err = self.execute(iptables_cmd)
        if err:
            log.log_screen_store(f"{self.devices_no}-执行添加 iptables 规则失败 user_id={user_id}-{err} pacakge_name={package_name}",
                                 level=ERROR)
            return False
        return True

    def uninstall(self, package_name: str):
        """卸载应用"""
        # quit
        # quit_cmd = f"{Config.ADB} shell am force-stop {package_name}"
        # _, err1 = self.execute(quit_cmd)
        # if err1:
        #     log.log_screen_store(f"强制退出应用{package_name}异常-{err1}", level=WARN)
        # uninstall_cmd = f"{Config.ADB} uninstall {package_name}"
        uninstall_cmd = f"""{config.adb} -s {self.devices_no} uninstall {package_name} """
        _, err2 = self.execute(uninstall_cmd, timeout=120)
        if err2:
            log.log_screen_store(f"{self.devices_no}-卸载应用{package_name}异常", level=WARN)

    def reboot(self):
        """重启手机或者模拟器"""
        cmd = f"{config.adb} -s {self.devices_no} reboot"
        _, err = self.execute(cmd)
        time.sleep(20)

    @staticmethod
    def activate_app(package_name: str, driver: WebdriverTools):
        """激活APP"""
        driver.launch_app(package_name)

    def start_tcpdump(self, user_id: str, pcap_name: str):
        tcpdump_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'timeout {config.capture_timeout} tcpdump -i nflog:{user_id} -w {config.apk_sdcard_dir}/{pcap_name}' " """
        progress = self.execute(cmd=tcpdump_cmd, asyn=True)
        return progress

    def capture_netflow(self, user_id: str, package_name: str, pcap_name: str, screenshot_file_dir: str = None) -> bool:
        """抓取APP流量"""
        tcpdump_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'timeout {config.capture_timeout} tcpdump -i nflog:{user_id} -w {config.apk_sdcard_dir}/{pcap_name}' " """
        log.log_screen_store(f"{self.devices_no}-执行 tcpdump 抓包 user_id={user_id} package_name={package_name}")
        progress = self.execute(cmd=tcpdump_cmd, asyn=True)
        success = True
        if progress:
            log.log_screen_store(f"{self.devices_no}-启动应用 package_name={package_name}")
            try:
                message = self.start_app(package_name, screenshot_file_dir)
                success = message["success"]
                progress.wait(config.capture_timeout)
            except subprocess.TimeoutExpired:
                pass
            except Exception as e:
                log.log_screen_store(f"{self.devices_no}-抓包可能异常! package_name={package_name}-{str(e)}")
                success = False
        else:
            log.log_screen_store(f"{self.devices_no}-执行 tcpdump 抓包失败 user_id={user_id} pacakge_name={package_name}", level=ERROR)
            success = False
        return success

    def access_site(self, driver: WebdriverTools, site: str):
        """点击网页"""
        driver.switch_to("NATIVE_APP")
        element = driver.find_element(AppiumBy.ID, "com.android.chrome:id/url_bar")
        driver.click(element)
        driver.send_keys(element, site)
        driver.enter()

    def capture_netflow_browser(self, user_id: str, link: WebElement or str, pcap_name: str,
                                driver: WebdriverTools = None) -> bool:
        """抓取浏览器流量"""
        tcpdump_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'timeout {config.capture_timeout} tcpdump -i nflog:{user_id} -w {config.apk_sdcard_dir}/{pcap_name}' " """
        log.log_screen_store(f"{self.devices_no}-执行 tcpdump 抓包 user_id={user_id} in browser")
        progress = self.execute(cmd=tcpdump_cmd, asyn=True)
        success = True
        if progress:
            try:
                if isinstance(link, str):
                    self.access_site(driver, link)
                else:
                    link.click()
                progress.wait(config.capture_timeout)
            except subprocess.TimeoutExpired:
                pass
            except Exception as e:
                log.log_screen_store(f"{self.devices_no}-浏览器执行抓包异常!-{str(e)}", level=WARN)
                success = False
        else:
            log.log_screen_store(f"{self.devices_no}-执行 tcpdump 抓包失败 user_id={user_id}", level=ERROR)
            success = False
        return success

    def pull_pcap_to_local(self, user_id: str, package_name: str = "", pcap_name: str = "") -> bool:
        """拉取pcap包到本地"""
        success = True
        local_path = os.path.join(config.local_tmp_dir, pcap_name)
        pull_pcap_cmd = f"""{config.adb} -s {self.devices_no} pull {config.apk_sdcard_dir}/{pcap_name} {local_path} """
        out, err = self.execute(pull_pcap_cmd, timeout=600)
        if err:
            log.log_screen_store(f"{self.devices_no}-拉取{pcap_name}到本地{config.local_tmp_dir}失败-{err} "
                                 f"user_id={user_id} package_name={package_name}")
            success = False
        return success

    def push_bytedance_plugin(self):
        """将字节跳动UI驱动插件推送到模拟器{ps：由于偶发的模拟器异常，会导致插件丢失}"""
        jar1 = os.path.join(config.local_tmp_dir, 'framework.jar')
        jar2 = os.path.join(config.local_tmp_dir, 'fastbot-thirdpart.jar')
        jar3 = os.path.join(config.local_tmp_dir, 'monkeyq.jar')
        try_time = 0
        while try_time < 3:
            failed = 0
            for item in [jar1, jar2, jar3]:
                push_cmd = f"{config.adb} -s {self.devices_no} push {item} {config.apk_sdcard_dir}"
                out, err = self.execute(push_cmd)
                if err:
                    failed += 1
            if failed > 0:
                time.sleep(10)
                try_time += 1
            else:
                return
        log.log_screen_store(f"推送framework.jar、fastbot-thirdpart.jar、monkeyq.jar失败", level=WARN)

    def screen_shot(self, screenshot_file_dir: str, file_name: int) -> bool:
        """屏幕截图"""
        scr_cmd = f"""{config.adb} -s {self.devices_no} shell screencap -p /sdcard/{file_name}.png"""
        out, err = self.execute(scr_cmd)
        if err:
            return False
        """截图保存至本地"""
        pull_cmd = f"""{config.adb} -s {self.devices_no} pull /sdcard/{file_name}.png {screenshot_file_dir}"""
        out, err = self.execute(pull_cmd)
        if err:
            return False
        """删除模拟器截图"""
        rf_cmd = f"""{config.adb} -s {self.devices_no} shell rm -f /sdcard/{file_name}.png"""
        out, err = self.execute(rf_cmd)
        if err:
            return False
        return True

    def screen_record(self, screenshot_file_dir: str, package_name: str) -> bool:
        """拉取录屏文件"""
        pull_cmd = f"{config.adb} -s {self.devices_no} pull {config.apk_sdcard_dir}/{package_name}.mp4 {screenshot_file_dir}"
        out, err = self.execute(pull_cmd, timeout=600)
        if err:
            return False
        del_cmd = f"{config.adb} -s {self.devices_no} shell rm -f {config.apk_sdcard_dir}/{package_name}.mp4"
        out, err = self.execute(del_cmd)
        return True

    def remove_pcap(self, pcap_name: str):
        """删除pcap包"""
        rf_cmd = f"""{config.adb} -s {self.devices_no} shell "su -c 'rm -f {config.apk_sdcard_dir}/{pcap_name}' " """
        self.execute(rf_cmd)

    def try_capture(self, user_id: str, package_name: str, app_id: str = '', screenshot_file_dir: str = '') -> dict:
        """抓取流量"""
        message = {"success": True, "remark": "", "pcap": ""}
        current_timestamp = int(time.time())
        pcap_name = f"{app_id}_{current_timestamp}.pcap"
        self.clear_environment(user_id)
        log.log_screen_store(f"{self.devices_no}-添加 iptables 规则 user_id={user_id} package_name={package_name}")
        if not self.add_iptables(user_id, package_name):
            message["success"] = False
            message["remark"] = "抓包异常"
            return message
        if not os.path.exists(screenshot_file_dir):
            os.makedirs(screenshot_file_dir)
        success = self.capture_netflow(user_id, package_name, pcap_name, screenshot_file_dir)
        log.log_screen_store(f"{self.devices_no}-删除 iptables 规则 user_id={user_id} package_name={package_name}")
        self.clear_environment(user_id)
        if success:
            log.log_screen_store(f"{self.devices_no}-拉取{pcap_name}到本地{config.local_tmp_dir} user_id={user_id} package_name={package_name}")
            success = self.pull_pcap_to_local(user_id, package_name, pcap_name)
            if not success:
                message["success"] = False
                message["remark"] = "抓包异常"
            self.remove_pcap(pcap_name)
        else:
            message["success"] = False
            message["remark"] = "程序运行异常"
        message["pcap"] = os.path.join(config.local_tmp_dir, pcap_name)
        return message

    def try_capture_browser(self, user_id: str, link: WebElement or str, l_class_id: str, b_class_id: str,
                            app_name: str, app_type: str = "web", driver: WebdriverTools = None) -> dict:
        """浏览器抓取流量"""
        message = {"success": True, "remark": "", "pcap": ""}
        _name = transform_app_name(app_name)
        pcap_name = f"{b_class_id}_{l_class_id}_{_name}_{app_type}.pcap"
        self.clear_environment(user_id)
        log.log_screen_store(f"{self.devices_no}-添加 iptables 规则 user_id = {user_id}")
        if not self.add_iptables(user_id):
            message["success"] = False
            message["remark"] = "抓包异常"
            return message
        success = self.capture_netflow_browser(user_id, link, pcap_name, driver)
        log.log_screen_store(f"{self.devices_no}-删除 iptables 规则 user_id={user_id} in browser")
        self.clear_environment(user_id)
        if success:
            log.log_screen_store(f"{self.devices_no}-拉取{pcap_name}到本地{config.local_tmp_dir} user_id={user_id} in browser")
            success = self.pull_pcap_to_local(user_id, "", pcap_name)
            if not success:
                message["success"] = False
                message["remark"] = "抓包异常"
            self.remove_pcap(pcap_name)
        else:
            message["success"] = False
            message["remark"] = "抓包异常"
        message["pcap"] = os.path.join(config.local_tmp_dir, pcap_name)
        return message

    def get_third_app_list(self) -> list:
        """获取第三方应用列表"""
        third_app_cmd = f"{config.adb} -s {self.devices_no} shell pm list packages -3"
        out, err = self.execute(third_app_cmd)
        if err:
            log.log_screen_store(f"{self.devices_no}-获取第三方应用列表失败", level=ERROR)
            return []
        apps = out.split(config.LF)
        return apps

    def clear_app_cache(self, package_name: str):
        """清理APP缓存"""
        clear_cmd = f"{config.adb} -s {self.devices_no} shell pm clear {package_name}"
        out, err = self.execute(clear_cmd)
        if err:
            log.log_screen_store(f"{package_name}-缓存清理失败", level=WARN)
        else:
            log.log_screen_store(f"{package_name}-缓存清理成功")

    def return_home(self):
        """返回桌面"""
        cmd = f"{config.adb} -s {self.devices_no} shell input keyevent 3"
        self.execute(cmd)

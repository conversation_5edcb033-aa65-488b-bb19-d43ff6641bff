# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   test.py
@Time    :   2023/6/7 19:33
"""
import os
import csv
import subprocess
from scapy.all import *


def _encoding(info: bytes, err: bytes) -> (str, str):
    codes = ["utf-8", "gbk"]
    for _encode in codes:
        try:
            return str(info, encoding=_encode), str(err, encoding=_encode)
        except Exception:
            pass
    return str(info), str(err)


def execute(cmd: str, timeout=600, asyn=False) -> (str, str) or subprocess.Popen:
    """执行命令"""
    out, err = "", ""
    try:
        result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  shell=True)
        if not asyn:
            out, err = result.communicate(timeout=timeout)
            return _encoding(out, err)
        else:
            return result
    except Exception as e:
        print(str(e))
        return out, err if not asyn else None


def test():
    source = r"D:\log\analysis_result-2023-06-08-104041.csv"
    new = r"D:\log\analysis_result-2023-06-08-1040418888888.csv"
    processed = []
    with open(source, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            pcap = row[5]
            if '.eth.pcap' in pcap:
                processed.append(row)
            else:
                eth_convert = "/home/<USER>"
                convert_cmd = f"""{eth_convert} {pcap} b2:36:67:5e:40:21 68:91:d0:65:3c:37 """
                out, err = execute(convert_cmd)
                if err:
                    print("转换ETH头异常", str(err))
                else:
                    eth_pcap = pcap.replace(".pcap", ".eth.pcap")
                    row[5] = eth_pcap
                processed.append(row)
    header = ['小类名称', '小类ID', '大类ID', 'package', 'activity', 'pcap', 'remark', 'feature_path']
    if not os.path.exists(new):
        with open(new, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
    with open(new, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in processed:
            writer.writerow(row)


def test2():
    file1 = r"D:\log\analysis_result-2023-06-08-104041-eth.csv"
    file2 = r"D:\log\analysis_result--测试无法确定WEB站点.csv"
    file3 = r"D:\log\analysis_result-2023-06-20-104041.csv"
    dict1 = {}
    dict2 = {}
    list1 = []
    with open(file1, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            name = row[0]
            dict1[name] = row
    with open(file2, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            name = row[0]
            dict2[name] = row
    header = ['小类名称', '小类ID', '大类ID', 'package', 'activity', 'pcap', 'remark', 'feature_path']
    if not os.path.exists(file3):
        with open(file3, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
    for key, value in dict1.items():
        if key in dict2:
            list1.append(dict2[key])
        else:
            list1.append(value)
    with open(file3, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in list1:
            writer.writerow(row)


def fake_pcap():
    pass


if __name__ == "__main__":
    fake_pcap()

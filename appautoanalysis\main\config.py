# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   config.py
@Time    :   2023/5/18 17:13
"""
import os
import platform
from base.settings import YamlConfig, JsonConfig


system = platform.system()


class Config(object):
    ETH_CONVERT = "eth_convert"
    BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    ID = "id"
    NAME = "小类名称"
    L_CLASS = "小类ID"
    STORE = "store"
    L_EN_NAME = "小类英文名称"
    B_NAME = "大类名称"
    B_EN_NAME = "大类英文名称"
    B_CLASS = "大类ID"
    CORRELATION_ID = "关联ID列表"
    CORRELATION = "correlation"
    REMARK = "remark"
    PACKAGE = "package"
    PCAP = "pcap"
    SUCCESS = "success"
    FOUND = "found"
    MOBILE_IP = "mobile_ip"
    ROUTER_HOST = "router_host"
    ROUTER_PORT = "router_port"
    EXTEND_DEVICE = "extend_device"
    START_TIME = "start_time"
    END_TIME = "end_time"
    PCAP_BEFORE = "pcap_before"
    PCAP_AFTER = "pcap_after"
    IS_BLOCKED = "blocked"
    SUSPICIOUS_DOMAINS = "suspicious_domains"
    SCREENSHOT = "screenshot"
    APP_ID = "app_id"
    APP_URL = "download_url"
    BEFORE = "before"
    AFTER = "after"
    sdk_result = "sdk_result"
    RESULT_SHEET = "sheet"
    WEIXIN_LOGIN = "weixin_login"
    BLOCK_CONDITION = "block_condition"
    IGNORE_NETWORK_PROTOCOL = ['764', '776', '777', '778']
    WEB_SITE = '761'
    SEARCH_URL = "https://www.baidu.com"
    SITE_TOOL = "https://www.beianx.cn/"
    LF = "\n" if system == "Linux" else "\r\n"

    CN_ANDROID = "cn_android"
    EN_ANDROID = "en_android"
    CN_IOS = "cn_ios"
    EN_IOS = "en_ios"
    CN_ANDROID_IDENTIFY = "cn_android_identify"
    EN_ANDROID_IDENTIFY = "en_android_identify"

    CMD1 = "update_cn_android"
    CMD2 = "update_en_android"
    CMD3 = "update_cn_ios"
    CMD4 = "update_en_ios"
    CMD5 = "device_status"
    CMD6 = "update_cn_android_identify"
    CMD7 = "update_en_android_identify"

    def __init__(self):
        conf1 = os.path.join(Config.BASE_PATH, "config", "settings.yaml")
        self.settings = YamlConfig.settings(conf1)
        conf2 = os.path.join(Config.BASE_PATH, "config", "devices.json")
        self.devices = JsonConfig.settings(conf2)

    @property
    def adb(self):
        return self.settings.get("TOOLS", {}).get("ADB")

    @property
    def aapt(self):
        return self.settings.get("TOOLS", {}).get("AAPT")

    @property
    def tshark(self):
        return self.settings.get("TOOLS", {}).get("TSHARK")

    @property
    def capture_queue_length(self):
        # 抓包队列长度
        return self.settings.get("SETTINGS", {}).get("CAPTURE_QUEUE_LENGTH")

    @property
    def queue_length(self):
        # 进程队列长度
        return self.settings.get("SETTINGS", {}).get("QUEUE_LENGTH")

    @property
    def apps_filename(self):
        return self.settings.get("SETTINGS", {}).get("APPS_FILENAME")

    @property
    def local_tmp_dir(self):
        return self.settings.get("SETTINGS", {}).get("LOCAL_TMP_DIR")

    @property
    def cmd_execute_timeout(self):
        return self.settings.get("SETTINGS", {}).get("CMD_EXECUTE_TIMEOUT")

    @property
    def capture_timeout(self):
        return self.settings.get("SETTINGS", {}).get("CAPTURE_TIMEOUT")

    @property
    def apk_sdcard_dir(self):
        return self.settings.get("SETTINGS", {}).get("APK_SDCARD_DIR")

    @property
    def chrome_driver(self):
        return self.settings.get("SETTINGS", {}).get("CHROME_DRIVER")

    @property
    def app_install_timeout(self):
        return self.settings.get("SETTINGS", {}).get("APP_INSTALL_TIMEOUT")

    @property
    def result_excel(self):
        return self.settings.get("SETTINGS", {}).get("RESULT_EXCEL")

    @property
    def result_xml(self):
        return self.settings.get("SETTINGS", {}).get("RESULT_XML")

    @property
    def capture_devices(self):
        return self.devices.get("capture_devices")

    @property
    def analysis_devices(self):
        return self.devices.get("analysis_devices")

    @property
    def mysql_host(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_HOST")

    @property
    def mysql_port(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_PORT")

    @property
    def mysql_user(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_USER")

    @property
    def mysql_password(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_PASSWORD")

    @property
    def mysql_database(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_DATABASE")

    @property
    def mysql_charset(self):
        return self.settings.get("SETTINGS", {}).get("MYSQL_CHARSET")

    @property
    def URL_DOWNLODER_DIR(self):
        return self.settings.get("SETTINGS", {}).get("URL_DOWNLODER_DIR")

    @property
    def SCREEN_SHOT_DIR(self):
        return self.settings.get("SETTINGS", {}).get("SCREEN_SHOT_DIR")

    @property
    def valid_apps_filename(self):
        return self.settings.get("SETTINGS", {}).get("VALID_APPS_FILENAME")

    @property
    def apk_timeout(self):
        return self.settings.get("SETTINGS", {}).get("APK_TIMEOUT")

    @property
    def privacy_permissions(self):
        return self.settings.get("SETTINGS", {}).get("PRIVACY_PERMISSIONS")

    @property
    def server_ip(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_IP")

    @property
    def server_port(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_PORT")

    @property
    def server_user(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_USER")

    @property
    def server_password(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_PASSWORD")

    @property
    def server_pcap_path(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_PCAP_PATH")

    @property
    def remote_dir(self):
        return self.settings.get("SETTINGS", {}).get("REMOTE_DIR")

    @property
    def mode(self):
        return self.settings.get("SETTINGS", {}).get("MODE")

    @property
    def local_app_db(self):
        return self.settings.get("SETTINGS", {}).get("LOCAL_APP_DB")

    @property
    def mac_proxy(self):
        return self.settings.get("SETTINGS", {}).get("MAC_PROXY")

    @property
    def mac_proxy_host(self):
        return self.settings.get("SETTINGS", {}).get("MAC_PROXY_HOST")

    @property
    def mac_proxy_port(self):
        return self.settings.get("SETTINGS", {}).get("MAC_PROXY_PORT")

    @property
    def proxy_server_host(self):
        return self.settings.get("SETTINGS", {}).get("PROXY_SERVER_HOST")

    @property
    def proxy_server_port(self):
        return self.settings.get("SETTINGS", {}).get("PROXY_SERVER_PORT")

    @property
    def script_db(self):
        return self.settings.get("SETTINGS", {}).get("SCRIPT_DB")

    @property
    def platform_type(self):
        return self.settings.get("SETTINGS", {}).get("PLATFORM_TYPE")

    @property
    def ios_interference(self):
        return self.settings.get("SETTINGS", {}).get("IOS_INTERFERENCE")

    @property
    def net(self):
        return self.settings.get("SETTINGS", {}).get("NET")

    @property
    def server_nan_ip(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_ANA_IP")

    @property
    def server_nan_user(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_ANA_USER")

    @property
    def server_nan_password(self):
        return self.settings.get("SETTINGS", {}).get("SERVER_ANA_PASSWORD")

    @property
    def ana_path(self):
        return self.settings.get("SETTINGS", {}).get("ANA_PATH")

    @property
    def eth_convert_path(self):
        return self.settings.get("SETTINGS", {}).get("ETH_CONVERT_PATH")


config = Config()

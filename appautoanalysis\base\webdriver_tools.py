# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   common.py
@Time    :   2023/7/7 9:45
"""
from appium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from appium.webdriver.common.touch_action import TouchAction
from .logger import log, WARN


def device_driver(device: dict, app_package: str = "", app_activity: str = "", browser: bool = False):
    desired_caps = dict()
    desired_caps['udid'] = device.get("udid")
    desired_caps['platformName'] = device.get("platformName")
    desired_caps['platformVersion'] = device.get("platformVersion")
    desired_caps['deviceName'] = device.get("deviceName")
    desired_caps['noReset'] = device.get("noReset")
    desired_caps['adbExecTimeout'] = device.get('adbExecTimeout')
    desired_caps['newCommandTimeout'] = device.get("newCommandTimeout")
    desired_caps['unicodeKeyboard'] = device.get("unicodeKeyboard")
    desired_caps['resetKeyboard'] = device.get("resetKeyboard")
    if not browser:
        desired_caps['appPackage'] = app_package
        desired_caps['appActivity'] = app_activity
        desired_caps['automationName'] = device.get("automationName")
    else:
        desired_caps['automationName'] = device.get("browser", {}).get("automationName")
        desired_caps['browserName'] = device.get("browser", {}).get("browserName")
        desired_caps['chromedriverExecutable'] = device.get("browser", {}).get("chromedriverExecutable")
    driver = webdriver.Remote('http://{ip}:{port}/wd/hub'.format(
        ip=device.get("ip"), port=device.get("port")), desired_caps)
    driver.implicitly_wait(10)
    return driver


class WebdriverTools(object):

    def __init__(self, web_driver: webdriver.Remote):
        self.web_driver = web_driver
        self.timeout = 15
        self.poll_frequency = 0.5

    def is_element_exists(self, text):
        """
        判断元素是否存在
        :param text:
        :return:
        """
        source = self.page_source
        if text in source:
            return True
        else:
            return False

    @property
    def page_source(self):
        return self.web_driver.page_source


    @property
    def current_activity(self):
        return self.web_driver.current_activity

    def is_enable(self, element):
        """
        判断元素是否可见
        :param element:
        :return:
        """
        page_html = self.page_source
        if element in page_html:
            return True
        else:
            return False

    def find_element(self, *locator, timeout: int = 15):
        """
        元素查找封装
        :param locator: (by, value)
        :param timeout: 15
        :return: element
        ID = "id"
        XPATH = "xpath"
        LINK_TEXT = "link text"
        PARTIAL_LINK_TEXT = "partial link text"
        NAME = "name"
        TAG_NAME = "tag name"
        CLASS_NAME = "class name"
        CSS_SELECTOR = "css selector"
        """
        try:
            element = WebDriverWait(self.web_driver, timeout, self.poll_frequency).\
                until(lambda x: x.find_element(*locator), 'element not found!')
            if element:
                return element
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else "An unknown server-side error"
            return None

    def find_elements(self, *locator, timeout: int = 15):
        try:
            elements = WebDriverWait(self.web_driver, timeout, self.poll_frequency).\
                until(lambda x: x.find_elements(*locator), 'elements not found!')
            if elements:
                return elements
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else ""
            return []

    def tap_position(self, positions, duration=200):
        try:
            self.web_driver.tap(positions, duration)
        except Exception as e:
            print(f'Oop location err {str(e)}')

    def l_press(self, el=None, positions=None, msg_duration=1000):
        """
        长按某个元素
        :param el:
        :param positions:
        :param msg_duration:
        :return:
        """
        if positions:
            x, y = positions
        else:
            x, y = None, None
        action = TouchAction(self.web_driver)
        action.long_press(el, x, y).wait(msg_duration).release().perform()

    def click(self, element):
        """
        点击事件
        :param element: 点击的元素
        :return:
        """
        try:
            element.click()
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else ""
            log.log_screen_store(f"点击元素失败！-{msg}", level=WARN)

    def clear(self, element):
        """
        :param element:
        :return:
        """
        try:
            element.clear()
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else ""
            log.log_screen_store(f"清除输入框数据失败！-{msg}", level=WARN)

    def send_keys(self, element, info):
        try:
            element.send_keys(info)
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else ""
            log.log_screen_store(f"输入框填充数据失败！-{msg}", level=WARN)

    def auto_wait(self, times):
        """
        隐式等待
        :param times: 时长，秒
        :return:
        """
        self.web_driver.implicitly_wait(times)

    def swipe_to(self, flag='up', duration=1000):
        """
        param flag: swipe way, like up, down, left or right
        param duration: time to take the swipe , in ms
        """
        # get screen size
        screen_size = self.web_driver.get_window_size()
        screen_w = screen_size.get('width')
        screen_h = screen_size.get('height')

        # up
        if flag == 'up':
            self._swipe_up(screen_w, screen_h, duration)
        # down
        elif flag == 'down':
            self._swipe_down(screen_w, screen_h, duration)
        # left
        elif flag == 'left':
            self._swipe_left(screen_w, screen_h, duration)
        # rig
        elif flag == 'right':
            self._swipe_right(screen_w, screen_h, duration)
        elif flag == 'back':
            self._swipe_back_up(screen_w, screen_h, duration)
        else:
            print('error flag, use: "up, down, left or right"')

    def _swipe_up(self, w, h, duration):
        """
        swipe to up
        :param w: screen width
        :param h: screen height
        :param duration: time to take the swipe , in ms
        """
        self.web_driver.swipe(start_x=w * 0.5, start_y=h * 0.75, end_x=w * 0.5, end_y=h * 0.25, duration=duration)

    def _swipe_down(self, w, h, duration):
        """
        swipe to down
        :param w: screen width
        :param h: screen height
        :param duration: time to take the swipe , in ms
        """
        self.web_driver.swipe(start_x=w * 0.5, start_y=h * 0.25, end_x=w * 0.5, end_y=h * 0.75, duration=duration)

    def _swipe_left(self, w, h, duration):
        """
        swipe to left
        :param w: screen width
        :param h: screen height
        :param duration: time to take the swipe , in ms
        """
        self.web_driver.swipe(start_x=w * 0.85, start_y=h * 0.5, end_x=w * 0.15, end_y=h * 0.5, duration=duration)

    def _swipe_back_up(self, w, h, duration):
        """
        swipe to back up
        :param w: screen width
        :param h: screen height
        :param duration: time to take the swipe , in ms
        """
        self.web_driver.swipe(start_x=w * 0.15, start_y=h * 0.5, end_x=w * 0.15, end_y=h * 0.5, duration=duration)

    def _swipe_right(self, w, h, duration):
        """
        swipe to right
        :param w: screen width
        :param h: screen height
        :param duration: time to take the swipe , in ms
        """
        self.web_driver.swipe(start_x=w * 0.15, start_y=h * 0.5, end_x=w * 0.85, end_y=h * 0.5, duration=duration)

    def quit(self):
        try:
            self.web_driver.quit()
        except Exception as e:
            pass
            # log.log_screen_store(f"退出driver异常-{str(e)}", level=WARN)

    def return_home(self):
        """返回HOME"""
        try:
            self.web_driver.press_keycode(3)
        except Exception as e:
            log.log_screen_store(f"return home exception: {str(e)}", level=WARN)

    def back(self):
        """返回"""
        try:
            self.web_driver.press_keycode(4)
        except Exception as e:
            log.log_screen_store(f"return back exception: {str(e)}", level=WARN)

    def enter(self):
        try:
            self.web_driver.press_keycode(66)
        except Exception as e:
            log.log_screen_store(f"enter exception: {str(e)}", level=WARN)

    def get(self, content: str):
        self.web_driver.get(content)

    def switch_to(self, context: str):
        """切换原生APP/其他驱动类型"""
        self.web_driver.switch_to.context(context)

    def uninstall(self, package_name: str):
        """卸载应用"""
        try:
            if self.web_driver.is_app_installed(package_name):
                self.web_driver.remove_app(package_name)
        except Exception as e:
            log.log_screen_store(f"卸载应用{package_name}异常-{str(e)}")

    def launch_app(self, package_name: str):
        """启动应用"""
        try:
            self.web_driver.activate_app(package_name)
        except Exception as e:
            log.log_screen_store(f"activating {package_name} exception: {str(e)}", level=WARN)

    def alert_accept(self):
        """弹窗接收"""
        try:
            self.web_driver.switch_to.alert.accept()
        except Exception:
            pass

    def save_screenshot(self, file_path):
        """屏幕截图"""
        try:
            self.web_driver.get_screenshot_as_file(file_path)
        except Exception:
            pass

    def terminate(self, package_name: str):
        try:
            self.web_driver.terminate_app(package_name)
        except Exception as e:
            pass

TOOLS:
  ADB: 'D:\\Software\\android-sdk-windows\\platform-tools\\adb.exe'
  AAPT: 'D:\\Software\\android-sdk-windows\\build-tools\\29.0.3\\aapt.exe'
  TSHARK: "D:\\Software\\Wireshark\\tshark.exe"
  TESSERACT: "D:\\Software\\Tesseract-OCR\\tesseract.exe"

SETTINGS:
  CAPTURE_QUEUE_LENGTH: 100                                                 # 抓包队列长度
  QUEUE_LENGTH: 1000                                                        # 进程队列长度
  APPS_FILENAME: "apps_data.csv"                                            # 应用文件名
  VALID_APPS_FILENAME: "cn_ios-2024-03-25-162305.csv"                                     # 有效应用
  LOCAL_TMP_DIR: "E:\\tmp\\tmp"                                             # 处理文件目录
  CMD_EXECUTE_TIMEOUT: 1800                                                  # 命令执行超时时间（秒）
  APK_TIMEOUT: 300                                                          # 应用运行超时时间 （秒）
  CAPTURE_TIMEOUT: 300                                                      # 抓包超时时间（秒）
  APK_SDCARD_DIR: "/sdcard"                                                 # 设备临时储存目录
  CHROME_DRIVER: "D:\\software\\chromedriver_win32\\chromedriver.exe"       # chrome driver
  APP_INSTALL_TIMEOUT: 3600                                                 # 应用安装超时时间（秒）
  RESULT_EXCEL: ""                                                          # 持续写入结果文件
  RESULT_XML: "feature_result.xml"                                          # 持续写入特征文件
  URL_DOWNLODER_DIR: "E:\\tmp\\apk"                    # 应用下载目录
  SCREEN_SHOT_DIR: "E:\\tmp\\tmp\\en_android"                                          # 视频录制目录
  MYSQL_HOST: "***************"
  MYSQL_PORT: 3306
  MYSQL_USER: "root"
  MYSQL_PASSWORD: "Root123456!"
  MYSQL_DATABASE: "apps"
  MYSQL_CHARSET: "utf8"
  MODE: 1                                                                     # 0 阻断；1 抓包
  LOCAL_APP_DB: 0                                                             # 0 不启用本地APP库；1 启用本地APP库
  SERVER_IP: "**************"
  SERVER_PORT: 9527
  SERVER_USER: "root"
  SERVER_PASSWORD: "4646"
  SERVER_PCAP_PATH: "/home/<USER>/"
  NET: "ens1f1"
  SERVER_ANA_IP: "**************"
  SERVER_ANA_USER: "root"
  SERVER_ANA_PASSWORD: "zaq1,lp-0629nhg"
  ANA_PATH: "/data1/default/ok/parse_data.sh"
  ETH_CONVERT_PATH: "/home/<USER>/eth_convert"
  REMOTE_DIR: "/root/xdr_extract/server/config"
  MAC_PROXY: 0                                                              # 0 禁用mac代理；1 启用mac代理
  MAC_PROXY_HOST: "**************"
  MAC_PROXY_PORT: 9527
  PLATFORM_TYPE: 5                                                          # 平台类型：1 国内安卓；2 国外安卓；3 国内IOS；4 国外IOS；5 识别
  PROXY_SERVER_HOST: "0.0.0.0"
  PROXY_SERVER_PORT: 5505
  SCRIPT_DB: "E:\\workspace\\AutomationToolDevelopment\\RecordScriptManager\\instance\\record_script_manager.db"
  IOS_INTERFERENCE:
    - 使用App时允许
    - 无线局域网与蜂窝网络
    - 要求App不跟踪
    - 允许
    - 继续

# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   verification.py
@Time    :   2023/8/31 17:13
"""
import os
import xml.dom.minidom
import pymysql
import datetime
import json
import subprocess
import re
import random
import win32api
import zipfile
import pypackerdetect
import paramiko
import plistlib

AAPT = r'D:\software\android-sdk_r24.4.1-windows\android-sdk-windows\build-tools\29.0.3\aapt.exe'
KEYTOOL = '"C:\\Program Files\\Java\jdk-21\\bin\\keytool.exe"'
APKTOOL = r"D:\workspace\appautoanalysis\main\apktool.jar"
JAVA = '"C:\\Program Files\\Java\\jdk-21\\bin\\java.exe"'

SOURCE_IP = "*************"

MAC_PROXY = "**************"
USERNAME = "brd-cd-rj"
PASSWORD = "zaq1,lp-"

STEADYS = ["加壳", "代码混淆", "数据加密", "动态加载", "数字签名"]

WORM_CODE = ["030104", "030105", "030106", "030108"]

TROJAN_CODE = ["030203", "030204", "030205", "030206", "030209"]


ZOMBIE_CODE = ["030301", "030302", "030303", "030304", "030305", "030306", "030307"]

MOBILE_CODE = ["030401", "030402", "030403", "030404", "030405", "030406", "030407", "030408"]

RANSOM_CODE = ["030500"]

MINE_CODE = ["030601", "030602", "030603"]

OTHER_MAL_CODE = ["030700"]

SUFFIXS = {
    "exe": 1,
    "apk": 2,
    "bas": 3,
    "bin": 4,
    "cgi": 5,
    "class": 6,
    "dir": 7,
    "msi": 8,
    "psm": 9,
    "ipa": 10,
    "sis": 11,
    "rsc": 12,
    "jad": 13,
    "mrp": 14,
    "mpkg": 15,
    "pkg": 16,
    "pxl": 17,
    "elf": 18,
    "app": 19,
    "bat": 20,
    "dll": 21,
    "dlg": 22,
    "dbf": 23,
    "cc": 24,
    "csc": 25,
    "iso": 26,
    "ace": 27,
    "rar": 28,
    "zip": 29,
    "7z": 30,
    "gz": 31,
    "cab": 32,
    "cap": 33,
    "bak": 34,
    "cfg": 35,
    "ch": 36,
    "chk": 37,
    "cmd": 38,
    "drv": 39,
    "htx": 40,
    "jar": 41,
    "java": 42,
    "js": 43,
    "jsp": 44,
    "jspx": 45,
    "lib": 46,
    "rom": 47,
    "tmp": 48,
    "xll": 49,
    "com": 50,
    "cer": 51,
    "crt": 52,
    "py": 53,
    "vbs": 54,
    "cad": 55,
    "chm": 56,
    "doc": 57,
    "docx": 58,
    "dot": 59,
    "maq": 60,
    "mpp": 61,
    "pdf": 62,
    "xml": 63,
    "txt": 64,
    "ppt": 65,
    "pptx": 66,
    "xls": 67,
    "xlsx": 68,
    "rtf": 70,
    "eml": 71,
    "box": 72,
    "cca": 73,
    "ccm": 74,
    "html": 75,
    "htm": 75,
    "javascript": 76,
    "gif": 77,
    "jpg": 78,
    "png": 79,
    "tiff": 80,
    "swf": 81,
    "flv": 82,
    "so": 83,
    "dex": 84,
    "hap": 85
}

shell_features = {
    "libchaosvmp.so": "娜迦",
    "libddog.so": "娜迦",
    "libfdog.so": "娜迦",
    "libedog.so": "娜迦企业版",
    "libexec.so": "爱加密",
    "libexecmain.so": "爱加密",
    "ijiami.dat": "爱加密",
    "ijiami.ajm": "爱加密企业版",
    "libsecexe.so": "梆梆免费版",
    "libsecmain.so": "梆梆免费版",
    "libSecShell.so": "梆梆免费版",
    "libDexHelper.so": "梆梆企业版",
    "libDexHelper-x86.so": "梆梆企业版",
    "libprotectClass.so": "360",
    "libjiagu.so": "360",
    "libjiagu_art.so": "360",
    "libjiagu_x86.so": "360",
    "libegis.so": "通付盾",
    "libNSaferOnly.so": "通付盾",
    "libnqshield.so": "网秦",
    "libbaiduprotect.so": "百度",
    "aliprotect.dat": "阿里聚安全",
    "libsgmain.so": "阿里聚安全",
    "libsgsecuritybody.so": "阿里聚安全",
    "libmobisec.so": "阿里聚安全",
    "libtup.so": "腾讯",
    "libshell.so": "腾讯",
    "mix.dex": "腾讯",
    "mixz.dex": "腾讯",
    "libtosprotection.armeabi.so": "腾讯御安全",
    "libtosprotection.armeabi-v7a.so": "腾讯御安全",
    "libtosprotection.x86.so": "腾讯御安全",
    "libnesec.so": "网易易盾",
    "libAPKProtect.so": "APKProtect",
    "libkwscmm.so": "几维安全",
    "libkwscr.so": "几维安全",
    "libkwslinker.so": "几维安全",
    "libx3g.so": "顶像科技",
    "libapssec.so": "盛大",
    "librsprotect.so": "瑞星"
}

SDK = {

    "com.shunwang.service.alarm": "顺网恶意SDK",
    "com.karumi.dexter.DexterActivity": "Android动态权限申请",
    "com.github.dfqin.grantor.PermissionActivity": "Android动态权限申请",
    "com.yanzhenjie.permission.PermissionActivity": "Android动态权限申请",
    "pub.devrel.easypermissions.AppSettingsDialogHolderActivity": "Android动态权限申请",
    "com.igexin.sdk.GActivity": "个推开机自启动组件",
    "com.yy.pushsvc.impl.KeepAliveActivity": "个推开机唤醒组件",

    "com.pad.android_independent_video_sdk.view.IndependentVideoActivity": "多盟广告平台",
    "com.qq.e.ads.ADActivity": "腾讯广告联盟",
    "com.qq.e.comm.DownloadService": "腾讯广告联盟",
    "com.baidu.mobads.AppActivity": "百度广告联盟",
    "com.xiaomi.gamecenter.sdk.ui.MiActivity": "小米移动广告联盟",
    "com.xiaomi.gamecenter.sdk.ui.PayListActivity": "小米移动广告联盟",
    "com.xiaomi.hy.dj.HyDjActivity": "小米移动广告联盟",
    "com.xiaomi.gamecenter.push.GamePushService": "小米移动广告联盟",
    "com.xiaomi.gamecenter.push.OnClickReceiver": "小米移动广告联盟",
    "com.xiaomi.gamecenter.sdk.utils.MiFileProvider": "小米移动广告联盟",
    "com.xiaomi.gamecenter.sdk.ui.fault.ViewFaultNoticeActivity": "小米移动广告联盟",
    "com.xiaomi.gamecenter.sdk.ui.notice.NoticeActivity": "小米移动广告联盟",
    "com.xiaomi.push.service.XMPushService": "小米消息推送",
    "com.xiaomi.push.service.XMJobService": "小米消息推送",
    "com.xiaomi.mipush.sdk.PushMessageHandler": "小米消息推送",
    "com.xiaomi.mipush.sdk.MessageHandleService": "小米消息推送",
    "com.xiaomi.push.service.receivers.NetworkStatusReceiver": "小米消息推送",
    "com.xiaomi.push.service.receivers.PingReceiver": "小米消息推送",
    "com.xiaomi.assemble.control.HmsPushReceiver": "小米消息推送",
    "com.xiaomi.assemble.control.DistributeActivity": "小米消息推送",
    "com.xiaomi.assemble.control.COSPushMessageService": "小米消息推送",
    "com.taobao.accs.ChannelService$KernelService": "淘宝推送",
    "com.taobao.accs.internal.AccsJobService": "淘宝推送",
    "com.taobao.accs.ServiceReceiver": "淘宝推送",
    "com.taobao.accs.data.MsgDistributeService": "淘宝推送",
    "com.taobao.agoo.AgooCommondReceiver": "淘宝推送",
    "com.alipay.pushsdk.thirdparty.hw.HuaWeiPushReceiver": "支付宝推送",
    "com.mob.MobProvider": "MobTech推送",
    "com.meizu.cloud.pushsdk.SystemReceiver": "魅族推送",
    "com.meizu.cloud.pushsdk.NotificationService": "魅族推送",
    "com.yy.pushsvc.thirdparty.PushVivoPushReceiver": "个推消息推送",
    "com.yy.pushsvc.thirdparty.PushMeizuPushReceiver": "个推消息推送",
    "com.yy.pushsvc.services.PushOppoMsgService": "个推消息推送",
    "com.yy.pushsvc.services.PushGTIntentService": "个推消息推送",
    "com.yy.pushsvc.services.GeTuiService": "个推消息推送",
    "com.igexin.sdk.PushActivity": "个推消息推送",
    "com.igexin.sdk.PushService": "个推消息推送",
    "com.yy.pushsvc.impl.PushOppoActivity": "个推消息推送",
    "com.yy.pushsvc.thirdparty.PushHuaweiPushReceiver": "个推消息推送",
    "com.yy.pushsvc.JobSchedulerService": "个推消息推送",
    "com.huawei.hms.update.provider.UpdateProvider": "华为消息推送",
    "com.huawei.hms.support.api.push.PushEventReceiver": "华为消息推送",
    "com.huawei.android.pushagent.PushBootReceiver": "华为消息推送",
    "com.huawei.hms.activity.BridgeActivity": "华为消息推送",
    "com.huawei.push.service.receivers.HWPushMessageHandler": "华为消息推送",
    "com.huawei.hms.support.api.push.service.HmsMsgService": "华为消息推送",
    "com.huawei.updatesdk.service.deamon.download.DownloadService": "华为消息推送",
    "com.huawei.android.hms.agent.common.HMSAgentActivity": "华为消息推送",
    "com.umeng.message.provider.MessageProvider": "友盟消息推送",
    "com.umeng.message.UmengMessageIntentReceiverService": "友盟消息推送",
    "com.umeng.message.UmengDownloadResourceService": "友盟消息推送",
    "com.umeng.message.UmengMessageCallbackHandlerService": "友盟消息推送",
    "com.umeng.message.NotificationProxyBroadcastReceiver": "友盟消息推送",
    "com.umeng.message.XiaomiIntentService": "友盟消息推送",
    "com.umeng.message.UmengIntentService": "友盟消息推送",
    "cn.jpush.android.service.PluginVivoMessageReceiver": "极光消息推送",
    "cn.jpush.android.service.PluginMeizuPlatformsReceiver": "极光消息推送",
    "cn.jpush.android.service.PluginHuaweiPlatformsReceiver": "极光消息推送",
    "cn.jpush.android.service.PluginXiaomiPlatformsReceiver": "极光消息推送",
    "cn.jiguang.user.service.action": "极光消息推送",
    "com.netease.nimlib.service.ResponseService": "网易云消息推送",
    "com.netease.nimlib.mixpush.mz.MZPushReceiver": "网易云消息推送",
    "com.netease.nimlib.ipc.NIMContentProvider": "网易云消息推送",
    "com.netease.nimlib.service.ResponseReceiver": "网易云消息推送",
    "com.netease.nimlib.service.NimReceiver": "网易云消息推送",
    "com.netease.nimlib.job.NIMJobService": "网易云消息推送",
    "com.netease.nimlib.service.NimService": "网易云消息推送",
    "com.vivo.push.sdk.LinkProxyClientActivity": "vivo消息推送",
    "com.vivo.push.sdk.service.CommandClientService": "vivo消息推送",

    "com.alipay.sdk.app.H5PayActivity": "支付宝支付接口",
    "com.unionpay.uppay.PayActivity": "银联支付接口",
    "com.jdpaysdk.author.browser.BrowserActivity": "银联支付接口",

    "com.baidu.location.f": "定位,百度地图API接口",
    "com.tencent.qalsdk.service.QalService": "通讯,腾讯云通信API接口",
    "com.tencent.qalsdk.service.QalAssistServices": "通讯,腾讯云通信API",
    "com.tencent.qalsdk.QALBroadcastReceiver": "通讯,腾讯云通信API",
    "com.tencent.qalsdk.core.NetConnInfoCenter": "通讯,腾讯云通信API",
    "com.tencent.tauth.AuthActivity": "第三方登陆,腾讯登入授权API",
    "com.tencent.tinker.lib.service.TinkerPatchForeService": "腾讯热更新API",
    "com.tencent.tinker.lib.service.DefaultTinkerResultService": "腾讯热更新API",
    "com.tencent.tinker.loader.hotplug.ActivityStubs$SIStub_02_T": "腾讯热更新API",
    "com.tencent.connect.common.AssistActivity": "腾讯热更新API",
    "com.tencent.tinker.lib.service.TinkerPatchService": "腾讯热更新API",
    "com.tencent.tinker.lib.service.TinkerPatchService$InnerService": "腾讯热更新API",
    "com.tencent.av.screen.MediaProjectionPermissionCheckActivity": "腾讯API,弹窗并请求屏幕录制/截屏权限",
    "tinker.sample.android.service.SampleResultServic": "Android热修复",
    "com.google.ar.core.InstallActivity": "Google AR设备API",
    "com.google.ar.core.min_apk_version": "Google AR设备API",
}


class ScpClient(object):

    def __init__(self):
        self.client = None
        self.scp = None

    def connect(self, server_ip: str, username: str, password) -> bool:
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        flag = True
        try:
            self.client.connect(server_ip, 22, username, password)
            self.scp = self.client.open_sftp()
        except Exception as e:
            print(f"scp 连接失败")
            flag = False
        return flag

    def transmission(self, local_path: str, remote_path: str) -> bool:
        flag = True
        try:
            self.scp.put(local_path, remote_path)
        except Exception as e:
            print(f"上传文件{local_path}失败")
            flag = False
        return flag

    def execute(self, command: str, timeout=30):
        output, error = "", ""
        try:
            stdin, stdout, stderr = self.client.exec_command(command, timeout)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
        except Exception as e:
            print(e)
        return output, error

    def close(self):
        if self.scp:
            self.scp.close()
        if self.client:
            self.client.close()


def get_md5_from_mysql(cursor) -> list:
    sql = "select sampleMd5List from file_monitor_rules"
    cursor.execute(sql)
    data = cursor.fetchall()
    return data


def get_malware_samples() -> tuple:
    path = r"E:\工作\8期\8期\拨测\tmp\samples.txt"
    samples = list()
    samples_desc = dict()
    current = ""
    with open(path, encoding="utf-8") as f:
        lines = f.readlines()
    for index, line in enumerate(lines):
        desc = ""
        list_item = line.split("\t")
        if len(list_item) >= 2:
            item = list_item[0]
            desc = list_item[1]
        else:
            item = list_item[0]
        if index == 0:
            current = os.path.dirname(item)
        sample = os.path.basename(item)
        sample = sample.split("_")[0]
        sample = sample.replace("\n", '')
        if sample == '\n':
            continue
        if sample not in samples:
            samples.append(sample)
            samples_desc[sample] = desc
    return samples, current, samples_desc


def get_malware_samples2() -> list:
    path = r"D:\data\boce\samples.txt"
    samples = list()
    with open(path, encoding="utf-8") as f:
        lines = f.readlines()
    for line in lines:
        item = line.split("\t")[0]
        sample = os.path.basename(item)
        sample = sample.split("_")[0]
        if sample not in samples:
            samples.append(sample)
    with open(r"D:\data\boce\mal_samples7.txt", 'w+', encoding="utf-8") as c:
        for item in samples:
            c.write(item)
            c.write("\n")
    return samples


def get_down_samples():
    path = r"E:\工作\8期\8期\拨测\tmp\down_samples.txt"
    samples = list()
    with open(path, encoding="utf-8") as f:
        lines = f.readlines()
    for line in lines:
        sample = line.replace("\n", "").split(" ")[-1]
        samples.append(sample)
    return samples


def clear(connection):
    clear_sql = "TRUNCATE TABLE new_md5_tb"
    cursor = connection.cursor()
    cursor.execute(clear_sql)
    connection.commit()
    cursor.close()


def update(data: list, connection):
    clear(connection)
    cursor = connection.cursor()
    for sample in data:
        if not sample:
            continue
        insert_sql = "insert into new_md5_tb(feed_name, feed_time, feed_type, feed_value) values(%s, %s, %s, %s)"
        str_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute(insert_sql, ("malware", str_time, "md5", sample))
    connection.commit()
    cursor.close()


def create_dir(path: str):
    try:
        if not os.path.exists(path):
            create_dir(os.path.split(path)[0])
        else:
            return
        os.mkdir(path)
    except Exception as e:
        print(f"创建目录失败-{str(e)}")


def _encoding(info: bytes, err: bytes) -> (str, str):
    codes = ["utf-8", "gbk"]
    for _encode in codes:
        try:
            return str(info, encoding=_encode), str(err, encoding=_encode)
        except Exception:
            pass
    return str(info), str(err)


def execute(cmd: str, timeout=30) -> (str, str) or subprocess.Popen:
    """执行命令"""
    out, err = "", ""
    try:
        result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  shell=True)
        out, err = result.communicate(timeout=timeout)
        return _encoding(out, err)
    except subprocess.TimeoutExpired as e:
        return "", ""
    except Exception as e:
        print(e)
        return "", ""


def apk_id(apk_path: str):
    cmd1 = f"{AAPT} dump badging {apk_path}"
    out1, err1 = execute(cmd1)
    app_id, platform, company = "", "", ""
    if out1:
        match = re.compile("package: name='(\S+)'").match(out1)
        if match:
            app_id = match.group(1)
    cmd2 = f"{KEYTOOL} -printcert -jarfile {apk_path}"
    out2, err2 = execute(cmd2)
    if out2:
        match2 = re.compile("O=(\S+),").search(out2)
        if match2:
            company = match2.group(1)
    return app_id, platform, company


def shell_detect(apk_file: str) -> tuple:
    fake_app, steady = 1, random.choice(STEADYS)
    try:
        zip_files = zipfile.ZipFile(apk_file)
        name_list = zip_files.namelist()
        for file_name in name_list:
            item = os.path.basename(file_name)
            if item in shell_features:
                fake_app = 1
                steady = shell_features[item]
                break
    except Exception as e:
        print(e)
    return fake_app, steady


def exe_shell(exe_file: str) -> tuple:
    fake_app, steady = 1, random.choice(STEADYS)
    try:
        pd = pypackerdetect.PyPackerDetect()
        dc = pd.detect(exe_file)
        if dc.get('detections'):
            fake_app = 1
            for item in dc.get('detections'):
                mt = re.compile("packer: \[([\s\S]*)\]").search(item)
                if mt:
                    steady = mt.group(1)
                    break
        if fake_app == 1:
            if not steady:
                steady = random.choice(STEADYS)
    except Exception as e:
        print(e)
    return fake_app, steady


def ipa_shell(ipa_file: str) -> tuple:
    filename = os.path.basename(ipa_file)
    fake_app, steady = 1, random.choice(STEADYS)
    scp_client = ScpClient()
    flag = scp_client.connect(MAC_PROXY, USERNAME, PASSWORD)
    if flag:
        flag2 = scp_client.transmission(ipa_file, f'/Users/<USER>/malware/{filename}')
        if flag2:
            cmd = f"otool -l /Users/<USER>/malware/{filename} | grep crypt"
            out, err = scp_client.execute(cmd)
            if out:
                mt = re.compile("cryptid (\d{1})").search(out)
                if mt:
                    cryptid = mt.group(1)
                    if cryptid == '1':
                        fake_app = 1
                        steady = random.choice(STEADYS)
    scp_client.close()
    return fake_app, steady


class AMParse:
    def __init__(self):
        self.activity = list()

    def get_activity_entry(self, node):
        if node.nodeName == "activity":
            self.activity.append(node.getAttribute('android:name'))

    def applicationtab(self, node):
        """解析application标签"""
        if node.nodeName == "application":
            for cn in node.childNodes:
                if cn.nodeName != "#text":
                    self.get_activity_entry(cn)

    def xml_parse(self, amxml: str):
        try:
            dom = xml.dom.minidom.parse(amxml)
            root = dom.documentElement
            nodelist = root.childNodes
            for node in nodelist:
                if node.nodeName != "#text":
                    self.applicationtab(node)
        except Exception as e:
            print(e)

    def sdk(self):
        sdks = list()
        for item in self.activity:
            if item in SDK:
                sdks.append(SDK.get(item))
        return sdks


def sdk_apk(apk_path: str) -> str:
    sdk_names = []
    current = os.path.dirname(apk_path)
    output = os.path.join(current, datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
    create_dir(output)
    cmd = f"{JAVA} -jar {APKTOOL} d -f {apk_path} -o {output}"
    execute(cmd, 60)
    amxml = os.path.join(output, 'AndroidManifest.xml')
    if os.path.exists(amxml):
        amparser = AMParse()
        amparser.xml_parse(amxml)
        sdk_names = amparser.sdk()
    return ",".join(sdk_names)


def ipa_id(ipa_path: str) -> tuple:
    current = os.path.dirname(ipa_path)
    app_id, platform, company, cur_macho = "", "", "", ""
    ipa_file = None
    try:
        ipa_file = zipfile.ZipFile(ipa_path)
        name_list = ipa_file.namelist()
        pattern = re.compile(r'Payload/[^/]*.app/Info.plist')
        for path in name_list:
            m = pattern.match(path)
            if m is not None:
                plist_path = m.group()
                plist_data = ipa_file.read(plist_path)
                plist_root = plistlib.loads(plist_data)
                app_id = plist_root['CFBundleIdentifier']
                macho = plist_path.split('/')[1].split('.')[0]
                macho_path = os.path.dirname(plist_path) + '/' + macho
                cur_macho = os.path.join(current, macho)
                ipa_file.extract(macho_path, cur_macho)
                cur_macho = os.path.join(cur_macho, 'Payload', f'{macho}.app', macho)
                break
    except Exception as e:
        print(e)
    finally:
        if ipa_file:
            ipa_file.close()

    return app_id, platform, company, cur_macho


def exe_id(exe_path: str) -> tuple:
    app_id, platform, company = "", "", ""
    try:
        lang, codepage = win32api.GetFileVersionInfo(exe_path, '\\VarFileInfo\\Translation')[0]
        info_path = u'\\StringFileInfo\\%04X%04X\\%s' % (lang, codepage, 'CompanyName')
        company = win32api.GetFileVersionInfo(exe_path, info_path)
        company = "" if company is None else company
    except Exception as e:
        print(e)
    return app_id, platform, company


def mal_class(desc: str = "") -> str:
    msg = desc.lower()
    if "trojan" in msg or '木马' in msg:
        return random.choice(TROJAN_CODE)
    elif "worm" in msg or '蠕虫' in msg:
        return random.choice(WORM_CODE)
    elif 'zombie' in msg or '僵尸' in msg:
        return random.choice(ZOMBIE_CODE)
    elif "ransom" in msg or '勒索' in msg:
        return random.choice(RANSOM_CODE)
    elif 'mine' in msg or '挖矿' in msg or 'bitcoin' in msg or '比特币' in msg:
        return random.choice(MINE_CODE)
    else:
        return random.choice(TROJAN_CODE+WORM_CODE)


def apk_class(desc: str = "") -> str:
    return random.choice(MOBILE_CODE)


def ipa_class(desc: str = "") -> str:
    return random.choice(MOBILE_CODE)


def exe_class(desc: str = "") -> str:
    return mal_class(desc)


def jar_class(desc: str = "") -> str:
    return random.choice(MOBILE_CODE)


def extract_apk(apk_path: str, suffix: str = "apk", desc: str = "") -> dict:
    info = dict({
        "state": 2,
        "score": 100,
        "appId": {"appId": "", "platform": "", "company": ""},
        "sdkNameList": "",
        "fakeApp": 2,
        "steady": "",
        "installFileType": suffix,
        "ip": SOURCE_IP,
        "class": ""
    })
    info["appId"]["appId"], info["appId"]["platform"], info["appId"]["company"] = apk_id(apk_path)
    info["fakeApp"], info["steady"] = shell_detect(apk_path)
    info["sdkNameList"] = sdk_apk(apk_path)
    info["class"] = apk_class(desc)
    return info


def extract_ipa(ipa_path: str, suffix: str = "ipa", desc: str = "") -> dict:
    info = dict({
        "state": 2,
        "score": 100,
        "appId": {"appId": "", "platform": "", "company": ""},
        "sdkNameList": "",
        "fakeApp": 2,
        "steady": "",
        "installFileType": suffix,
        "ip": SOURCE_IP,
        "class": ""
    })
    info["appId"]["appId"], info["appId"]["platform"], info["appId"]["company"], macho = ipa_id(ipa_path)
    info["fakeApp"], info["steady"] = ipa_shell(macho)
    info["class"] = ipa_class(desc)
    return info


def extract_exe(exe_path: str, suffix: str = "exe", desc: str = "") -> dict:
    info = dict({
        "state": 2,
        "score": 100,
        "appId": {"appId": "", "platform": "", "company": ""},
        "sdkNameList": "",
        "fakeApp": 2,
        "steady": "",
        "installFileType": suffix,
        "ip": SOURCE_IP,
        "class": ""
    })
    info["appId"]["appId"], info["appId"]["platform"], info["appId"]["company"] = exe_id(exe_path)
    info["fakeApp"], info["steady"] = exe_shell(exe_path)
    info["class"] = exe_class(desc)
    return info


def extract_jar(exe_path: str, suffix: str = "jar", desc: str = "") -> dict:
    info = dict({
        "state": 2,
        "score": 100,
        "appId": {"appId": "", "platform": "", "company": ""},
        "sdkNameList": "",
        "fakeApp": 1,
        "steady": random.choice(STEADYS),
        "installFileType": suffix,
        "ip": SOURCE_IP,
        "class": ""
    })
    info["class"] = jar_class(desc)
    return info


def malware_info_extract(samples: list, current: str, sample_desc: dict) -> dict:
    mal_info = {}
    for item in os.listdir(current):
        file_path = os.path.join(current, item)
        if not os.path.isfile(file_path):
            continue
        sample, _, suffix = item.split("_")
        suffix_code = SUFFIXS.get(suffix, 999)
        if sample not in samples:
            continue
        desc = sample_desc.get(sample, "")
        if suffix in ['apk']:
            mal_info[sample] = extract_apk(file_path, suffix_code, desc)
        elif suffix in ['ipa']:
            mal_info[sample] = extract_ipa(file_path, suffix_code, desc)
        elif suffix in ["exe"]:
            mal_info[sample] = extract_exe(file_path, suffix_code, desc)
        elif suffix in ['jar', 'hap']:
            mal_info[sample] = extract_jar(file_path, suffix_code, desc)
        else:
            mal_info[sample] = dict({
                "state": 2,
                "score": 100,
                "appId": {"appId": "", "platform": "", "company": ""},
                "sdkNameList": "",
                "fakeApp": 1,
                "steady": random.choice(STEADYS),
                "installFileType": suffix_code,
                "ip": SOURCE_IP,
                "class": mal_class(desc)
            })
    return mal_info


def verify():
    connection = pymysql.connect(host='**************',
                                 user='root',
                                 password='zaq1<LP_69',
                                 database='js_security_data',
                                 cursorclass=pymysql.cursors.DictCursor)
    print("""awk -F '\\$!' '{print $27}' cm_file_monitor.dat |sort|uniq -c""")
    down_samples = get_down_samples()
    mal_sample, current, sample_desc = get_malware_samples()
    filtered = filter(lambda x: x not in down_samples, mal_sample)
    filter_samples = list(filtered)
    print(filter_samples)
    update(filter_samples, connection)
    connection.close()
    mal_info = malware_info_extract(filter_samples, current, sample_desc)
    md5mal = r'E:\工作\8期\8期\拨测\tmp\malmd5.json'
    with open(md5mal, 'w') as f:
        json.dump(mal_info, f, ensure_ascii=False)
    print("success")
    print("vim /opt/se/etc/tjymdbconfig.json 文件里的md5_info改为update")


if __name__ == "__main__":
    """ tar -czvf samples.tar.gz ./* """
    """开始演练前，清空数据表，同时重新加载database(vim /opt/se/etc/tjymdbconfig.json)"""
    verify()
    # get_malware_samples2()

# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   test2.py
@Time    :   2023/6/25 14:22
"""
import struct
import difflib
import dpkt
import subprocess

# def main():
# # 打开pcap文件
#     with open(r'D:\log\weishi\705_18879_8c23510c2edb1c2af33358c10cdd7c81_app.eth.pcap', 'rb') as fin:
#         with open(r"D:\log\weishi\705_18879_8c23510c2edb1c2af33358c10cdd7c81_app2.pcap", "wb") as fout:
#             pcapout = dpkt.pcap.Writer(fout)
#             pcap = dpkt.pcap.Reader(fin)
#             # 创建一个列表，用于存储所有数据包
#             pkts = []
#             # 遍历每个数据包
#             for ts, buf in pcap:
#                 try:
#                     # 解析以太网帧、IP首部和TCP首部
#                     eth = dpkt.ethernet.Ethernet(buf)
#                     ip = eth.data
#                     tcp = ip.data
#
#                     # 检查是否为HTTP流量
#                     if isinstance(tcp, dpkt.tcp.TCP) and b'HTTP' in tcp.data:
#                         # 将TCP负载解析为HTTP请求/响应对象
#                         try:
#                             http = dpkt.http.Request(tcp.data)
#                             # 修改Host头部字段
#                             if http.headers.get('host'):
#                                 http.headers['host'] = 'new.example.com'
#                             # 更新TCP负载
#                             tcp.data = bytes(http)
#                         except Exception as e:
#                             print(1, e)
#                     if isinstance(tcp, dpkt.tcp.TCP):
#                         pseheader = ip.src + ip.dst + b'\x00' * 2 + b'\x00\x06' + struct.pack('>I', len(tcp))
#                         tcp.sum = 0
#                         all_tcp = tcp.pack()
#                         if len(all_tcp) % 2 != 0: all_tcp += b'\x00'
#                         tcp.sum = dpkt.in_cksum(pseheader + all_tcp)
#                         try:
#                             assert dpkt.in_cksum(pseheader + tcp.pack()) == 0, "校验失败"
#                         except Exception as e:
#                             print(2, e)
#                     if isinstance(tcp, dpkt.udp.UDP):
#                         pseheader = ip.src + ip.dst + b'\x00' * 2 + b'\0x17' + struct.pack('>I', len(tcp))
#                         tcp.sum = 0
#                         all_tcp = tcp.pack()
#                         if len(all_tcp) % 2 != 0: all_tcp += b'\x00'
#                         tcp.sum = dpkt.in_cksum(pseheader + all_tcp)
#                         try:
#                             assert dpkt.in_cksum(pseheader + tcp.pack()) == 0, "校验失败"
#                         except Exception as e:
#                             print(2, e)
#                     ip.data = tcp
#                     temp = dpkt.ethernet.Ethernet(src=eth.src, dst=eth.dst, type=eth.type, data=ip)
#                     # 添加新的数据包到列表中
#                     # pkts.append((ts, bytes(eth)))
#                     pcapout.writepkt(temp, ts=ts)
#                 except Exception as e:
#                     print(3, e)

# 保存为新的PCAP文件
# with open(r'D:\log\weishi\705_18879_8c23510c2edb1c2af33358c10cdd7c81_app2.pcap', 'wb') as f:
#     pcap_writer = dpkt.pcap.Writer(f)
#     for ts, pkt in pkts:
#         pcap_writer.writepkt(pkt, ts)


def test2():
    text = "pingan"
    com = "pazq"
    print(difflib.SequenceMatcher(None, text, com).quick_ratio())


def test():
    # 打开pcap文件
    with open(r'D:\log\weishi\705_18879_8c23510c2edb1c2af33358c10cdd7c81_app.eth.pcap', 'rb') as f:
        pcap = dpkt.pcap.Reader(f)
        # 创建一个列表，用于存储所有数据包
        pkts = []

        # 遍历每个数据包
        found = 0
        for ts, buf in pcap:
            try:
                # 解析以太网帧、IP首部和TCP首部
                eth = dpkt.ethernet.Ethernet(buf)
                ip = eth.data
                tcp = ip.data

                # 检查是否为HTTP流量
                if isinstance(tcp, dpkt.tcp.TCP) and b'HTTP' in tcp.data and found == 0:

                    # 将TCP负载解析为HTTP请求/响应对象
                    try:
                        http = dpkt.http.Request(tcp.data)
                        # 修改Host头部字段
                        if http.headers.get('host'):
                            http.headers['host'] = 'www.weishi.com'
                            # 更新TCP负载
                            tcp.data = bytes(http)
                            # 重新计算TCP、IP和以太网帧的长度
                            tcp.doff = 5 + len(http) // 4
                            tcp_len = len(tcp)
                            ip.len = len(ip.data) + tcp_len
                            eth.len = len(eth.data) + tcp_len
                            found += 1
                    except Exception as e:
                        print(1, e)
                # 添加新的数据包到列表中
                pkts.append((ts, bytes(eth)))

            except Exception as e:
                print(2, str(e))

    # 保存为新的PCAP文件
    with open(r'D:\log\weishi\705_18879_8c23510c2edb1c2af33358c10cdd7c81_app2.pcap', 'wb') as f:
        pcap_writer = dpkt.pcap.Writer(f)
        for ts, pkt in pkts:
            pcap_writer.writepkt(pkt, ts)


def launch_service():
    # cmd = "start D:\\workspace\\venv\\py38\\Scripts\\pymobiledevice3.exe developer dvt xcuitest com.facebook.WebDriverAgentRunner202402201420.xctrunner"
    cmd = "D:\\workspace\\venv\\py38\\Scripts\\pymobiledevice3.exe -h"
    sp = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    # out, err = sp.wait(timeout=100)
    out, err = sp.communicate(timeout=100)
    print(out, err)
    # sp.wait(timeout=100)
    print("cmd launched...")


if __name__ == "__main__":
    launch_service()
    # main()

# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   mouse_callback.py
@Time    :   2023/8/16 15:56
"""
import cv2
img = r"D:\code\appautoanalysis\resource\screenshot\19112-2023-08-16-111209.png"


def onmouse(event, x, y, flags, param):  # 标准鼠标交互函数
    if event == cv2.EVENT_MOUSEMOVE:  # 当鼠标移动时
        imgs = cv2.imread(img, 0)
        print(x, y, imgs[y, x])  # 显示鼠标所在像素坐标和数值，注意像素表示方法和坐标位置的不同


def win_name():
    cv2.namedWindow("img", 0)  # 构建窗口
    cv2.resizeWindow("img", 640, 480)
    cv2.setMouseCallback("img", onmouse)  # 回调绑定窗口
    imgs = cv2.imread(img, 0)
    while True:  # 无限循环
        cv2.imshow("img", imgs)  # 显示图像
        if cv2.waitKey() == ord('q'):
            break  # 按下‘q'键，退出
    cv2.destroyAllWindows()  # 关闭窗口


if __name__ == '__main__':
    win_name()

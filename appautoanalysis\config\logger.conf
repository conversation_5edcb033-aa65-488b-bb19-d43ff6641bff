[loggers]
keys=root,file,fileAndConsole

[handlers]
keys=file<PERSON><PERSON><PERSON>,consoleHandler

[logger_root]
level=DEBUG
handlers=consoleHandler

[logger_file]
level=DEBUG
handlers=fileHandler
qualname=file
propagate=1

[logger_fileAndConsole]
level=DEBUG
handlers=fileHandler,consoleHandler
qualname=fileAndConsole
propagate=0

[handler_consoleHandler]
class=StreamHandler
args=(sys.stdout,)
level=DEBUG
formatter=form01

[handler_fileHandler]
class=FileHandler
args=('%(logfilename)s', 'a')
level=DEBUG
formatter=form02

[formatters]
keys=form01, form02

[formatter_form01]
format=%(asctime)s-%(filename)s-%(lineno)d-%(levelname)s:%(message)s

[formatter_form02]
format=%(asctime)s-%(filename)s-%(lineno)d:%(message)s
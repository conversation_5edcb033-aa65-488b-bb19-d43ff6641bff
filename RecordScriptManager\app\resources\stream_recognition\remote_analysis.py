# -*- coding: utf-8 -*-
"""
@File      : remote_analysis.py
<AUTHOR> caokun
@Email     : <EMAIL>
@License   : (C) Copyright 2025, Broadtech
@Created   : 2025/7/28 9:25
@IDE       : PyCharm
"""
import os
import time
import logging
import paramiko
from contextlib import contextmanager
from typing import List, Dict, Optional

from app import db
from app.models import StreamRecognition, XdrDetail
from config import Config

logging.getLogger("paramiko.transport").setLevel(logging.WARNING)
logging.getLogger("paramiko.transport.sftp").setLevel(logging.WARNING)


class SSHConnectionPool:

    def __init__(self):
        self.connections = {}
        self.connection_timeout = 300

    def get_connection(self, connection_key: str, ip: str, username: str, password: str) -> Optional[paramiko.SSHClient]:
        if connection_key in self.connections:
            client = self.connections[connection_key]
            if self._is_connection_alive(client):
                return client
            else:
                self._cleanup_connection(connection_key)

        return self._create_connection(connection_key, ip, username, password)

    def _create_connection(self, connection_key: str, ip: str, username: str, password: str) -> Optional[paramiko.SSHClient]:
        try:
            client = paramiko.SSHClient()
            client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            client.connect(ip, 22, username, password, timeout=self.connection_timeout)
            self.connections[connection_key] = client
            return client
        except Exception as e:
            logging.error(f"[SSH] 连接失败: {connection_key} -> {ip}, 错误: {e}")
            return None

    @staticmethod
    def _is_connection_alive(client: paramiko.SSHClient) -> bool:
        try:
            transport = client.get_transport()
            return transport is not None and transport.is_active()
        except Exception:
            return False

    def _cleanup_connection(self, connection_key: str):
        if connection_key in self.connections:
            try:
                self.connections[connection_key].close()
            except Exception:
                pass
            del self.connections[connection_key]

    def disconnect_all(self):
        for key in list(self.connections.keys()):
            self._cleanup_connection(key)

    @contextmanager
    def get_ssh_session(self, connection_key: str, ip: str, username: str, password: str):
        client = self.get_connection(connection_key, ip, username, password)
        if client is None:
            raise ConnectionError(f"Failed to connect to {ip}")
        try:
            yield client
        except Exception:
            self._cleanup_connection(connection_key)
            raise


class DatabaseManager:

    @staticmethod
    def batch_save_xdr_details(xdr_data_list: List[Dict]) -> bool:
        try:
            xdr_details = []
            total_rows = 0
            for data in xdr_data_list:
                for row in data['rows']:
                    if len(row) < 10:
                        continue

                    xdr_detail = XdrDetail(
                        value=row[2],
                        xdr_type=row[0],
                        big_class=row[1],
                        name=data['stream_recognition'].name,
                        upstream_traffic=row[3],
                        downstream_traffic=row[4],
                        upstream_packets=row[5],
                        downstream_packets=row[6],
                        pcap_name=row[7],
                        proportion=row[8],
                        total=row[9],
                        stream_recognition_id=data['stream_recognition'].id
                    )
                    xdr_details.append(xdr_detail)
                    total_rows += 1

            if xdr_details:
                db.session.bulk_save_objects(xdr_details)
                db.session.commit()
                return True

            return False

        except Exception as e:
            logging.error(f"[DB] 保存失败: {e}")
            db.session.rollback()
            return False


class RemoteAnalysiser:

    def __init__(self):
        self.ssh_pool = SSHConnectionPool()
        self.db_manager = DatabaseManager()
        self.outdated_pcap = []
        self.processed_file_path = os.path.join(Config.BASEDIR, "tmp", "processed_files.txt")
        self.load_processed_files()

        self.config = {
            'server_ip': '**************',
            'server_user': 'root',
            'server_password': '4646',
            'local_tmp_dir': '/home/<USER>/',
            'net': 'ens1f1',
            'server_nan_ip': '**************',
            'server_nan_user': 'root',
            'server_nan_password': 'zaq1,lp-0629nhg',
            'ana_path': '/data1/default/ok/parse_data.sh',
            'eth_convert_path': '/home/<USER>/eth_convert'
        }

    def load_processed_files(self):
        try:
            if os.path.exists(self.processed_file_path):
                with open(self.processed_file_path, 'r', encoding='utf-8') as f:
                    self.outdated_pcap = [line.strip() for line in f.readlines() if line.strip()]
            else:
                self.outdated_pcap = []
        except Exception:
            self.outdated_pcap = []

    def save_processed_files(self):
        try:
            with open(self.processed_file_path, 'w', encoding='utf-8') as f:
                for pcap_name in self.outdated_pcap:
                    f.write(f"{pcap_name}\n")
        except Exception:
            pass

    def add_processed_file(self, pcap_name):
        if pcap_name not in self.outdated_pcap:
            self.outdated_pcap.append(pcap_name)
            self.save_processed_files()

    def disconnect_all(self):
        self.ssh_pool.disconnect_all()

    def get_remote_pcap_files(self) -> Dict[str, str]:
        pcap_files = {}

        try:
            with self.ssh_pool.get_ssh_session(
                'file_server',
                self.config['server_ip'],
                self.config['server_user'],
                self.config['server_password']
            ) as client:
                cmd = f"ls -la {self.config['local_tmp_dir']}"
                _, stdout, stderr = client.exec_command(cmd)

                if stdout is None:
                    logging.error("[FILES] 命令执行失败，stdout为空")
                    return pcap_files

                files_output = stdout.read().decode('utf-8')
                stderr_output = stderr.read().decode('utf-8') if stderr else ""

                if stderr_output:
                    logging.error(f"[FILES] 命令执行错误: {stderr_output}")
                    return pcap_files

                pcap_files = self._parse_pcap_files(files_output)

        except Exception as e:
            logging.error(f"[FILES] 获取文件列表失败: {e}")

        return pcap_files

    def _parse_pcap_files(self, files_output: str) -> Dict[str, str]:
        pcap_files = {}
        lines = files_output.strip().split('\n')

        for line in lines:
            if line.startswith('-') or line.startswith('d'):
                parts = line.split()
                if len(parts) >= 9:
                    filename = parts[-1]

                    if (filename.endswith(('.pcap', '.pcapng')) and
                            not filename.endswith('.eth.pcap')):

                        if filename.endswith('.pcap'):
                            pcap_name = filename[:-5]
                        elif filename.endswith('.pcapng'):
                            pcap_name = filename[:-7]
                        else:
                            continue

                        pcap_path = str(os.path.join(self.config['local_tmp_dir'], filename))
                        pcap_files[pcap_name] = pcap_path

        return pcap_files

    def _convert_eth_remote(self, pcap: str) -> str:
        try:
            with self.ssh_pool.get_ssh_session(
                'file_server',
                self.config['server_ip'],
                self.config['server_user'],
                self.config['server_password']
            ) as client:
                convert_cmd = f"{self.config['eth_convert_path']} {pcap} b2:36:67:5e:40:21 68:91:d0:65:3c:37"
                _, stdout, stderr = client.exec_command(convert_cmd)

                if stdout is None:
                    return pcap

                err = stderr.read().decode('utf-8') if stderr else ""
                if err:
                    return pcap

                eth_pcap = str(pcap.replace(".pcap", ".eth.pcap"))
                return eth_pcap

        except Exception:
            return pcap

    def replay_pcap(self, pcap_path: str) -> bool:
        try:
            with self.ssh_pool.get_ssh_session(
                'file_server',
                self.config['server_ip'],
                self.config['server_user'],
                self.config['server_password']
            ) as client:
                cmd = f"tcpreplay -i {self.config['net']} --pps=300 {pcap_path}"
                _, stdout, _ = client.exec_command(cmd)

                out = stdout.read().decode('utf-8')
                return "Successful" in out if out else False

        except Exception:
            return False

    def analysis_xdr(self, pcap_name: str) -> List[List[str]]:
        data = []
        try:
            with self.ssh_pool.get_ssh_session(
                'analysis_server',
                self.config['server_nan_ip'],
                self.config['server_nan_user'],
                self.config['server_nan_password']
            ) as client:
                cmd = f"sh {self.config['ana_path']} {pcap_name}"
                _, stdout, _ = client.exec_command(cmd)
                result = stdout.read().decode('utf-8')

                data = self._parse_xdr_result(result)

        except Exception:
            pass

        return data

    @staticmethod
    def _parse_xdr_result(result: str) -> List[List[str]]:
        data = []
        try:
            _, tmp, _ = result.split('++++++')
            tmp_s = tmp.split('\n')

            for item in tmp_s:
                if not item:
                    continue
                processed = list(map(lambda x: x.strip(), item.split('\t')))
                data.append(processed)
        except Exception:
            pass

        return data

    def process_single_pcap(self, pcap_name: str, pcap_path: str) -> bool:
        try:
            eth_pcap_path = self._convert_eth_remote(pcap_path)

            if not self.replay_pcap(eth_pcap_path):
                logging.error(f"[PROCESS] 回放失败: {pcap_name}")
                return False

            self.add_processed_file(pcap_name)

            time.sleep(600)

            analysis_result = self.analysis_xdr(pcap_name)
            if not analysis_result:
                logging.error(f"[PROCESS] 分析失败: {pcap_name}")
                return False

            success = self._save_analysis_result(pcap_name, analysis_result)
            return success

        except Exception as e:
            logging.error(f"[PROCESS] 处理异常: {pcap_name}, 错误: {e}")
            return False

    def _save_analysis_result(self, pcap_name: str, analysis_result: List[List[str]]) -> bool:
        try:
            stream_recognition = StreamRecognition.query.filter_by(pcap_name=pcap_name).first()
            if not stream_recognition:
                return False

            xdr_data = {
                'stream_recognition': stream_recognition,
                'rows': analysis_result
            }

            return self.db_manager.batch_save_xdr_details([xdr_data])

        except Exception:
            return False

    def remote_analysis(self):
        try:
            while True:
                self._process_analysis_cycle()
                time.sleep(300)

        finally:
            self.disconnect_all()

    def _process_analysis_cycle(self):
        try:
            pcap_dict = self.get_remote_pcap_files()
            if not pcap_dict:
                logging.info("[CYCLE] 没有发现pcap文件")
                return

            unprocessed_files = {
                name: path for name, path in pcap_dict.items()
                if name not in self.outdated_pcap
            }

            if not unprocessed_files:
                logging.info("[CYCLE] 所有文件都已处理过")
                return

            for pcap_name, pcap_path in unprocessed_files.items():
                success = self.process_single_pcap(pcap_name, pcap_path)
                if not success:
                    logging.error(f"[CYCLE] 文件处理失败: {pcap_name}")

        except Exception as e:
            logging.error(f"[CYCLE] 分析周期异常: {e}")
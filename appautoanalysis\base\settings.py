# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   settings.py
@Time    :   2023/6/29 11:23
"""
import yaml
import json


class YamlConfig(object):
    def __init__(self):
        pass

    @staticmethod
    def _read_yml(yml: str, encoding="utf-8"):
        with open(yml, encoding=encoding) as f:
            return yaml.load(f.read(), Loader=yaml.FullLoader)

    @staticmethod
    def settings(path: str):
        return YamlConfig._read_yml(path)


class JsonConfig(object):
    def __init__(self):
        pass

    @staticmethod
    def _read_json(path: str, encoding="utf-8") -> dict:
        with open(path, "r", encoding=encoding) as f:
            return json.load(f)

    @staticmethod
    def settings(path: str) -> dict:
        return JsonConfig._read_json(path)


# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   analysis_process.py
@Time    :   2023/5/24 9:48
"""
import difflib
import os
import re
import time
import traceback
from multiprocessing import Queue, Lock
import threading
import queue
from appium.webdriver.common.appiumby import AppiumBy
from pypinyin import lazy_pinyin, Style
from .logger import log, WARN, INFO, ERROR
from main.config import config
from .common import FileParsing, feature_extract, write_excel, write_json, transform_app_name, convert_eth
from .downloader import AppStoreCN, AppStoreIOS, AppStoreEN
from .webdriver_tools import device_driver, WebdriverTools


class PcapAnalysiser(object):
    """pcap分析器"""
    def __init__(self, _queue: Queue, lock: Lock, device: dict):
        self.queue = _queue
        self.lock = lock
        self.device = device
        self.sniffers = {}
        self.feature_parser = FileParsing()
        self.parse_feature_file()

    def parse_feature_file(self):
        """解析特征库文件"""
        path = os.path.join(config.local_tmp_dir, config.result_xml)
        if os.path.exists(path):
            self.sniffers = self.feature_parser.read_xml(path)

    def write_feature_file(self, data: dict):
        """写入特征文件"""
        path = config.result_xml
        self.feature_parser.write_xml(path, data, self.lock)

    def data_check(self, feature_list: list, name: str):
        """查询已知xml中最大value值并校验已有特征库"""
        message = {"pass": False, "remark": "failed", 'max_value': None, "feature": {}}
        value_list = []
        if not feature_list:
            message['remark'] = '此pcap包未提取出相关特征！'
            return message
        for sniffer in self.sniffers.get("sniffers", []):
            feature_name = sniffer.get("property", {}).get("name")
            if feature_name == name:
                message['remark'] = '应用已存在特征库中,请核实！'
                return message
            for string in sniffer.get("string", {}).get("property", []):
                _text = string.get('_text', '')
                if _text in feature_list:
                    message['remark'] = '应用特征已存在特征库中,请核实！'
                    message['feature']['repetitive_features'] = _text
                    return message
            value = sniffer.get("property", {}).get("value")
            value_list.append(int(value))
        if value_list:
            max_value = max(value_list)
        else:
            max_value = 0
        message['pass'] = True
        message['max_value'] = max_value
        message['remark'] = 'pass'
        return message

    # 对pcap包的提取结果进行二次特征提取
    def feature_extraction(self, pcap_path: str, file_name: str, company: str = ""):
        # 获取提取的所有结果
        feature_result = feature_extract(pcap_path)
        # 处理host与server_name特征列表
        host_list = feature_result['host']
        serverName_list = feature_result['server_name']
        merged_list = host_list + serverName_list
        host_server_list = list(set(merged_list))
        # 判断字符串中是否包含英文的正则表达式
        pattern = '[a-zA-Z]+'
        if re.search('[a-zA-Z]', file_name):
            match_start = re.search(pattern, file_name).start()
        else:
            match_start = len(file_name)
        # 拆分字符串中的中英文字符
        english_part = file_name[match_start:]
        chinese_part = file_name[:match_start]
        # 获取字符串拼音列表
        pinyin_list = lazy_pinyin(chinese_part)
        # 获取首字母缩写
        abbr = ''.join(lazy_pinyin(chinese_part, style=Style.FIRST_LETTER, errors='ignore'))
        result_list = []
        # 列出对应开发公司注册域名
        company_domain_list = self.icp_filing_query(company=company)
        for domain_name in host_server_list:
            domain_list = domain_name.split('.')
            # 判断英文字符是否存在域名中
            if english_part:
                if english_part.lower() in domain_name.lower():
                    for i in range(0, len(domain_list)):
                        if english_part.lower() in domain_list[i].lower():
                            result_domain = '.'.join(domain_list[i:])
                            result_list.append(result_domain)
                            continue
            # 判断拼音全拼是否在域名中（拼音应至少有两个在域名中）并给出满足条件的坐标
            coordinate = [i for i, x in enumerate(domain_list) if
                          sum([1 for j in pinyin_list if j.lower() in x.lower()]) >= 2]

            if coordinate:
                result_domain = '.'.join(domain_list[coordinate[0]:])
                result_list.append(result_domain)
                continue
            is_similarity = False
            # 判断拼音缩写相似度（相似度>70%）则取值
            for i in range(0, len(domain_list) - 1):
                similarity = difflib.SequenceMatcher(None, domain_list[i].lower(), abbr.lower()).quick_ratio()
                if similarity >= 0.7:
                    result_domain = '.'.join(domain_list[i:])
                    result_list.append(result_domain)
                    is_similarity = True
                    break
            if is_similarity:
                continue
            # 判断应用域名是否与开发公司对应
            company_list = self.icp_filing_query(domain=domain_name)
            if company in company_list:
                index = domain_name.find('.')
                result_domain = domain_name[index + 1:]
                result_list.append(result_domain)
                continue
            # 判断应用域名是否是开发公司所注册
            if domain_name in company_domain_list:
                index = domain_name.find('.')
                result_domain = domain_name[index + 1:]
                result_list.append(result_domain)
        result_list = list(set(result_list))
        return result_list

    # ICP备案查询
    def icp_filing_query(self, domain: str = "", company: str = ""):
        # 获取分析设备
        device_info = self.device
        package = device_info.get("browser", {}).get("app_package")
        activity = device_info.get("browser", {}).get("app_activity")
        # 获取设备驱动
        driver = device_driver(device_info, package, activity, browser=True)
        driver = WebdriverTools(driver)
        # 切换模式
        driver.switch_to("NATIVE_APP")
        element = driver.find_element(AppiumBy.ID, "com.android.chrome:id/search_box_text")
        # driver.click(element)
        # time.sleep(2)
        driver.send_keys(element, config.SITE_TOOL)
        driver.enter()
        time.sleep(3)
        driver.switch_to("CHROMIUM")
        element = driver.find_element(AppiumBy.XPATH, '//*[@id="keyword"]')
        result = []
        # ICP备案查询域名
        if domain:
            driver.send_keys(element, domain) if element else None
            driver.enter()
            element = driver.find_element(AppiumBy.XPATH, f'/html/body/table/tbody/tr[2]/td/a')
            if element:
                result.append(element.text)
            return result
        # ICP备案查询公司名
        if company:
            driver.send_keys(element, company) if element else None
            driver.enter()
            i = 1
            while True:
                element = driver.find_element(AppiumBy.XPATH, f'/html/body/table[{i}]/tbody/tr[6]/td/div/a')
                if element:
                    result.append(element.text)
                    i = i + 1
                else:
                    break
            # element = driver.find_element(AppiumBy.XPATH, '//*[@id="webName"]')
            # web_info['website'] = element.text
            return result

    # def analysis(self):
    #     """分析pcap"""
    #     # loop = asyncio.new_event_loop()
    #     # asyncio.set_event_loop(loop)
    #     # loop.run_until_complete(self.new_loop())
    #     self.new_loop()

    def analysis(self):
        while True:
            result_dict = dict()
            pcap_info = self.queue.get()
            pcap = pcap_info.get("pcap")
            name = pcap_info.get(config.NAME)
            company = pcap_info.get("company")
            log.log_screen_store(f"开始分析 {pcap}")
            # message = self.compare(pcap, name)
            eth_pcap = convert_eth(pcap)
            pcap_info["pcap"] = eth_pcap
            pcap_info.update()
            result = self.feature_extraction(pcap_info["pcap"], name, company)
            log.log_screen_store(f"结束分析 {pcap}")
            check_result = self.data_check(result, name)
            pcap_info.update(check_result)
            print(check_result['pass'])
            if check_result['pass']:
                self.write_result_xml(pcap_info, result)
            else:
                if check_result['feature']:
                    _name = transform_app_name(pcap_info.get(config.NAME))
                    feature_json = f"{pcap_info.get(config.B_CLASS)}_" \
                                   f"{pcap_info.get(config.L_CLASS)}_{_name}.json"
                    json_path = write_json(feature_json, pcap_info.get("feature", {}))
                    pcap_info["feature_path"] = json_path
                write_excel(pcap_info, pcap_info["sheet_name"], self.lock)

    """数据格式化并记录结果"""
    def write_result_xml(self, pcap_info, results: list):
        index = pcap_info['max_value']+1
        result_sniffer = dict()
        result_sniffer['sniffers'] = list()
        sniffer = dict()
        sniffer_list = list()
        servertype = str(pcap_info.get(config.B_CLASS)) + str(index)
        while True:
            if len(servertype) < 10:
                servertype = servertype + '0'
            else:
                break
        sniffer['property'] = {
            'name': pcap_info.get(config.NAME),
            'value': str(index),
            'classname': pcap_info.get(config.B_NAME),
            'class': pcap_info.get(config.B_CLASS),
            'servertype': servertype,
            'enname': pcap_info.get(config.L_EN_NAME),
            'enclassname': pcap_info.get(config.B_EN_NAME),
        }
        for result in results:
            sniffer_list.append({'source': 'host|server_name', '_text': result})
        sniffer['string'] = {'property': sniffer_list, 'type': 1}
        result_sniffer['sniffers'].append(sniffer)
        self.write_feature_file(result_sniffer)
        write_excel(pcap_info, pcap_info["sheet_name"], self.lock)

    def start(self):
        """启动分析"""
        # threading.Thread(target=self.analysis).start()
        self.analysis()


class DeviceThread(threading.Thread):
    """设备线程"""
    def __init__(self, app_queue: queue, multiprocess_queue: Queue, lock: Lock, write_lock: Lock, device: dict, *args, **kwargs):
        super(DeviceThread, self).__init__(*args, **kwargs)
        self.queue = app_queue
        self.lock = lock
        self.write_lock = write_lock
        self.device = device
        self.multiprocess_queue = multiprocess_queue
        self.sniffers = {}
        self.parse_sniffer()

    def parse_sniffer(self):
        """解析特征库文件"""
        path = os.path.join(config.local_tmp_dir, 'feature_result.xml')
        if os.path.exists(path):
            self.sniffers = FileParsing().read_xml(path)

    def _find_sniffer(self, name: str) -> dict:
        """查找特征节点"""
        for sniffer in self.sniffers.get("sniffers", []):
            feature_name = sniffer.get("property", {}).get("name")
            if feature_name == name:
                return sniffer
        return {}

    def run(self) -> None:
        if self.device.get("platform") == "Android":
            if config.platform_type in [1, 5]:
                app_driver = AppStoreCN(lock=self.lock, write_lock=self.write_lock, device_info=self.device)
            else:
                app_driver = AppStoreEN(lock=self.lock, write_lock=self.write_lock, device_info=self.device)
        elif self.device.get("platform") == "IOS":
            app_driver = AppStoreIOS(lock=self.lock, write_lock=self.write_lock, device_info=self.device)
        else:
            return
        while True:
            if app_driver.check():
                app_info = self.queue.get()
                try:
                    app_driver.start(app_info)
                except Exception as e:
                    log.log_screen_store(f"runtime error: {str(e)}", level=WARN)
                    time.sleep(10)
            else:
                log.log_screen_store(f"设备状态异常: {self.device.get('uuid')}", level=WARN)
                time.sleep(10)


def consumer_process(app_queue: queue, multiprocess_queue: Queue, lock: Lock, write_lock: Lock, device: dict):
    dt = DeviceThread(app_queue, multiprocess_queue, lock, write_lock, device)
    dt.start()

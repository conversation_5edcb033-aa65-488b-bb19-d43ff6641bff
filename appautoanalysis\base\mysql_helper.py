# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   app_analysis.py
@Time    :   2023/9/18 10:06
"""
import time
import sqlite3
import pymysql
from main.config import config
from .logger import log, ERROR, WARN


class MySqlHelper(object):

    def __init__(self):
        self.conn, self.cursor = None, None
        self._connect()

    def _connect(self):
        mysql_conf = {
            'host': config.mysql_host,
            'port': config.mysql_port,
            'user': config.mysql_user,
            'password': config.mysql_password,
            'db': config.mysql_database,
            'charset': config.mysql_charset
        }
        while True:
            try:
                log.log_screen_store("connect app database...")
                self.conn = pymysql.Connect(**mysql_conf)
                self.cursor = self.conn.cursor()
                break
            except Exception as e:
                log.log_screen_store(f"mysql connect failed({config.mysql_host}:{config.mysql_port}) {e}", level=ERROR)
                time.sleep(10)

    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

    def select(self, sql: str) -> list:
        data = []
        try:
            self.cursor.execute(sql)
            data = self.cursor.fetchall()
        except Exception as e:
            log.log_screen_store(f"sql execute exception({sql}) {e}", level=ERROR)
        return data

    def update(self, sql):
        return self._exec_sql(sql)

    def delete(self, sql):
        return self._exec_sql(sql)

    def insert(self, sql, args):
        return self._exec_sql(sql, args)

    def _exec_sql(self, sql: str, args=None):
        if type(args) == list:
            try:
                self.cursor.executemany(sql, args)
                self.conn.commit()
            except Exception as e:
                log.log_screen_store(f"sql execute error: {e}", level=ERROR)
                self.conn.rollback()
        else:
            if args:
                sql = sql % args
            try:
                self.cursor.execute(sql)
                self.conn.commit()
            except Exception as e:
                log.log_screen_store(f"sql execute error: {e}", level=ERROR)
                self.conn.rollback()


class MySqliteHelper(object):
    def __init__(self):
        self.conn, self.cursor = None, None

    def connect(self, db_path: str) -> bool:
        try:
            self.conn = sqlite3.connect(db_path)
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as err:
            log.log_screen_store("链接脚本数据库失败", level=ERROR)
        return False

    def query(self, cmd: str):
        data = []
        try:
            self.cursor.execute(cmd)
            data = self.cursor.fetchall()
        except sqlite3.Error as err:
            log.log_screen_store("查询脚本数据库失败", level=ERROR)
        return data

    def update(self, cmd: str, params: list = None):
        try:
            if params:
                self.conn.execute(cmd, params)
            else:
                self.conn.execute(cmd)
            self.conn.commit()
        except sqlite3.Error as err:
            log.log_screen_store(f"更新脚本数据库失败{err}", level=ERROR)
            self.conn.rollback()

    def close(self):
        if self.conn:
            self.conn.close()


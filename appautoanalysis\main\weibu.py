# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   weibu.py
@Time    :   2023/12/7 22:13
"""
import time
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

edge = r"D:\software\edgedriver_win64\msedgedriver.exe"

class WeiBuCheck(object):
    def __init__(self):
        self.driver = None
        self.poll_frequency = 0.5
        self.init_driver()

    def init_driver(self):
        self.driver = webdriver.Edge(edge)
        url = "https://x.threatbook.com/v5/domain/xred.mooo.com"
        self.driver.get(url)

    def quit(self):
        if self.driver:
            self.driver.quit()

    def find_element(self, *locator, timeout: int = 15):
        try:
            element = WebDriverWait(self.driver, timeout, self.poll_frequency).\
                until(lambda x: x.find_element(*locator), 'element not found!')
            if element:
                return element
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else "An unknown server-side error"
            return None

    def check(self, sample: str) -> str:
        timeout = 120
        content = "//*[@id='app']/div[1]/header/div[2]/div/div[1]/div/div/div[1]/div[1]/div[2]/textarea"
        try:
            element = self.find_element(By.XPATH, content, timeout=timeout)
            if element:
                element.click()
                lens = len(element.text)
                for _ in range(lens):
                    element.send_keys(Keys.BACKSPACE)
                element.send_keys(sample, Keys.ENTER)
                time.sleep(5)
                result = '//div[@class="judgments-result"]'
                element = self.find_element(By.XPATH, result, timeout=timeout)
                if element and element.text == "恶意":
                    return "Y"
        except Exception as e:
            print(e)
        return "N"


if __name__ == "__main__":
    wb = WeiBuCheck()
    samples = ["baidu.com", "xred.mooo.com", "sina.com", "modem.pw"]
    for item in samples:
        wb.check(item)
    wb.quit()








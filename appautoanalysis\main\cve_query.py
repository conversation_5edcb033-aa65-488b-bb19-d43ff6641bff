# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   cve_query.py
@Time    :   2023/12/22 16:51
"""

import time
import csv
import random
import requests
from lxml import etree
from selenium import webdriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys

edge = r"D:\software\edgedriver_win64\msedgedriver.exe"
source = r"D:\avd.csv"
result = r"D:\cve.csv"


class AvdCheck(object):
    def __init__(self):
        self.driver = None
        self.poll_frequency = 0.5
        self.init_driver()

    def init_driver(self):
        self.driver = webdriver.Edge(edge)
        url = "https://avd.aliyun.com/search?q=CVE-2020-13942"
        self.driver.get(url)

    def quit(self):
        if self.driver:
            self.driver.quit()

    def find_element(self, *locator, timeout: int = 15):
        try:
            element = WebDriverWait(self.driver, timeout, self.poll_frequency).\
                until(lambda x: x.find_element(*locator), 'element not found!')
            if element:
                return element
        except Exception as e:
            msg = e.msg if hasattr(e, 'msg') else "An unknown server-side error"
            return None

    def check(self, sample: str):
        timeout = 20
        content = "//*[@id='navbarsExampleDefault']/form/input"
        try:
            element = self.find_element(By.XPATH, content, timeout=timeout)
            if element:
                element.click()
                time.sleep(1)
                element.clear()
                element.send_keys(sample, Keys.ENTER)
                time.sleep(random.randint(3, 8))
                _result = "/html/body/main/div[2]/div/div[2]/table/tbody/tr/td[2]"
                element = self.find_element(By.XPATH, _result, timeout=timeout)
                if element and element.text != "":
                    self.write_result([sample, element.text])
        except Exception as e:
            print(e)

    @staticmethod
    def get_avd():
        data = []
        with open(source, 'r', encoding="UTF-8-sig") as csvfile:
            reader = csv.reader(csvfile)
            for index, row in enumerate(reader):
                if index == 0:
                    continue
                data.append(row[0])
        return data

    @staticmethod
    def get_queried():
        data = []
        with open(result, 'r', encoding="UTF-8-sig") as csvfile:
            reader = csv.reader(csvfile)
            for index, row in enumerate(reader):
                if index == 0:
                    continue
                data.append(row[0])
        return data

    @staticmethod
    def write_result(data: list):
        with open(result, 'a+', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(data)


if __name__ == "__main__":
    wb = AvdCheck()
    avds = wb.get_avd()
    queried = wb.get_queried()
    time.sleep(20)
    for item in avds:
        if item in queried:
            continue
        wb.check(item)
    wb.quit()

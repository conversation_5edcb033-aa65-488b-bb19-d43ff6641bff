# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   goudaowei
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   zernike_test.py
@Time    :   2023/11/17 15:16
"""

from scipy.spatial import distance as dist
import numpy as np
import mahotas
import cv2
import imutils

'''
运用Zernike矩阵量化图像中的形状。在图片中寻找某个特定的形状.
'''


def describe_shapes(image):
    shapeFeatures = []
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.G<PERSON>sianBlur(gray, (3, 3), 0)
    # cv2.imshow("2", blurred)
    thresh = cv2.threshold(blurred, 200, 255, cv2.THRESH_BINARY)[1]
    # thresh = cv2.dilate(thresh, None, iterations=4)
    # thres = cv2.erode(thresh, None, iterations=2)
    cv2.imshow("1", thresh)
    cv2.waitKey(0)

    _cnts = cv2.findContours(thresh.copy(), cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
    cnts = _cnts[0] if imutils.is_cv4() else _cnts[1]

    for c in cnts:
        mask = np.zeros(image.shape[:2], dtype="uint8")
        cv2.drawContours(mask, [c], -1, 255, -1)
        (x, y, w, h) = cv2.boundingRect(c)
        roi = mask[y:y + h, x:x + w]
        # cv2.imshow("roi", roi)
        # cv2.waitKey(0)
        features = mahotas.features.zernike_moments(roi, cv2.minEnclosingCircle(c)[1], degree=8)
        shapeFeatures.append(features)

    return cnts, shapeFeatures

logo = r'D:\workspace\appautoanalysis\resource\login\6.png'
login = r"D:\workspace\appautoanalysis\resource\screenshot\16984.png"

refImage = cv2.imread(logo)
(_cnts, gameFeatures) = describe_shapes(refImage)
shapesImage = cv2.imread(login)
(cnts, shapeFeatures) = describe_shapes(shapesImage)
D = dist.cdist(gameFeatures, shapeFeatures)
print(D)
i = np.argmin(D)  # 获取最小距离的下标

for (j, c) in enumerate(cnts):
    if i != j:
        box = cv2.minAreaRect(c)
        box = np.intp(cv2.boxPoints(box) if imutils.is_cv4() else cv2.boxPoints(box))
        cv2.drawContours(shapesImage, [box], - 1, (0, 0, 255), 2)

print(i)
print(len(cnts))
# ＃计算轮廓旋转边界
box = cv2.minAreaRect(cnts[i])
box = np.intp(cv2.boxPoints(box) if imutils.is_cv4() else cv2.boxPoints(box))
cv2.drawContours(shapesImage, [box], - 1, (0, 255, 0), 2)
(x, y, w, h) = cv2.boundingRect(cnts[i])
print(f"locatio: {x, y}")
cv2.putText(shapesImage, "FOUND!", (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 3)
cv2.imshow("Input Image", refImage)
cv2.imshow("Detected Shapes", shapesImage)
cv2.waitKey(0)
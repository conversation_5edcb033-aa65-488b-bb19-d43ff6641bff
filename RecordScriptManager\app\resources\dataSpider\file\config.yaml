baidu_app:
  headers:
    Accept: "application/json, text/plain, image/webp, */*"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    Connection: "keep-alive"
    Referer: "https://shouji.baidu.com/cateDetail?cateBoardid=board_101_0311&boardid=board_101_0311&boardId=34592&subCateName=%E5%85%A8%E9%83%A8&f0=cateDetail_cateDetailPage%400"
    Sec-Fetch-Dest: "empty"
    Sec-Fetch-Mode: "cors"
    Sec-Fetch-Site: "same-origin"
    User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0"
    sec-ch-ua: "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\""
    sec-ch-ua-mobile: "?0"
    sec-ua-platform: "\"Windows\""
  cookies:
    BAIDUID_BFESS: "F83CEA28190B5212F18EAC0CE33D6E59:FG=1"
    newlogin: "1"
    BDUSS: "dVYzVBNldvV2k5MWcxVUw2QWJGVmJ3WUd2SjlLdy1FcDQtdWpwNkR6MTlvUHhuSVFBQUFBJCQAAAAAAAAAAAEAAABbzmZHY8CkwKRrAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH0T1Wd9E9VnQX"
    BDUSS_BFESS: "dVYzVBNldvV2k5MWcxVUw2QWJGVmJ3WUd2SjlLdy1FcDQtdWpwNkR6MTlvUHhuSVFBQUFBJCQAAAAAAAAAAAEAAABbzmZHY8CkwKRrAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAH0T1Wd9E9VnQX"
    ai-studio-ticket: "DACB0D17BF1B4ECAA95C6D5016736C0A4F85BE46B7C140868C2EB6CEE19E06B9"
    jsdk-uuid: "188530ff-54c5-4559-8b1b-cc9b3e1db27d"
    RT: "\"z=1&dm=baidu.com&si=5b94a5c8-5658-4c83-b59e-deb3cf1f2180&ss=m8conqwy&sl=1&tt=38i&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=41p&ul=35k2&hd=35kn\""
    AGL_USER_ID: "0decf9a4-8b20-489b-b5c9-c62315cd2050"
  url: "https://shouji.baidu.com/api/board"

huawei_app:
  api:
    base_url: "https://web-drcn.hispace.dbankcloud.com/edge/uowap/index"
    interface_code_url: "https://web-drcn.hispace.dbankcloud.com/edge/webedge/getInterfaceCode"

  headers:
    common:
      Accept: "application/json, text/plain, */*"
      Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
      Cache-Control: "no-cache"
      Connection: "keep-alive"
      Origin: "https://appgallery.huawei.com"
      Pragma: "no-cache"
      Referer: "https://appgallery.huawei.com/"
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
      sec-ch-ua: "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\""
      sec-ch-ua-mobile: "?0"
      sec-ch-ua-platform: "\"Windows\""

  request:
    timeout: 10
    max_retries: 3
    base_delay: 2
    max_delay: 10

  params:
    default:
      serviceType: "20"
      maxResults: "25"
      zone: ""
      locale: "zh"

xiaomi_app:
  headers:
    Host: "app.market.xiaomi.com"
    Cookie: "cUserId=null; serviceToken=null"
    user-agent: "Dalvik/2.1.0 (Linux; U; Android 11; M2006J10C Build/RP1A.200720.011)"
  
  api:
    base_url: "https://app.market.xiaomi.com"
    category_url: "/apm/category/mainlist"
    featured_url: "/apm/featured"
  
  params:
    category:
      os: "V12.5.11.0.RJNCNXM"
      page: "0"
      sdk: "30"
    
    featured:
      combine: "1"
      os: "V12.5.11.0.RJNCNXM"
      sdk: "30"
      stamp: "0"
  
  request:
    timeout: 10
    max_retries: 3
    base_delay: 2
    max_delay: 10
    rate_limit_requests: 10
    rate_limit_window: 60

tencent_app:
  api:
    base_url: "https://yybadaccess.3g.qq.com/v2/dynamicard_yybhome"
    detail_url: "https://sj.qq.com/appdetail/{}"
    download_url_template: "https://imtt2.dd.qq.com/sjy.00009/sjy.00004/16891/apk/{md5}.apk?fsname={pkg_name}_{version}.apk"

  headers:
    authority: "yybadaccess.3g.qq.com"
    accept: "*/*"
    accept-language: "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7"
    content-type: "text/plain;charset=UTF-8"
    origin: "https://sj.qq.com"
    referer: "https://sj.qq.com/"
    sec-ch-ua: "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\""
    sec-ch-ua-mobile: "?0"
    sec-ch-ua-platform: "\"macOS\""
    sec-fetch-dest: "empty"
    sec-fetch-mode: "cors"
    sec-fetch-site: "same-site"
    user-agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

  request:
    timeout: 10
    retry_delay: 20
    base_delay: 1
    empty_threshold: 10
    game_limit: 2000
    batch_size: 24

  payloads:
    app: |
      {"head":{"cmd":"dynamicard_yybhome","authInfo":{"businessId":"AuthName"},"deviceInfo":{"platform":1},"userInfo":{"guid":"4e4e2e81-57c0-40c7-9e5b-8b0345a42a99"},"expSceneIds":"","hostAppInfo":{"scene":"app_center"}},"body":{"bid":"yybhome","offset":0,"size":10,"preview":false,"listS":{"region":{"repStr":["CN"]},"cate_alias":{"repStr":["all"]}},"listI":{"limit":{"repInt":[%s]},"offset":{"repInt":[%s]}},"layout":"YYB_HOME_APP_LIBRARY_LIST"}}
    game: |
      {"head":{"cmd":"dynamicard_yybhome","authInfo":{"businessId":"AuthName"},"deviceInfo":{"platform":1},"userInfo":{"guid":"4e4e2e81-57c0-40c7-9e5b-8b0345a42a99"},"expSceneIds":"92170","hostAppInfo":{"scene":"game_center"}},"body":{"bid":"yybhome","offset":0,"size":10,"preview":false,"listS":{"region":{"repStr":["CN"]},"tag_alias":{"repStr":["all"]}},"listI":{"limit":{"repInt":[%s]},"offset":{"repInt":[%s]}},"layout":"YYB_HOME_GAME_LIBRARY_LIST_ALGRITHM"}}

microsoft_app:
  api:
    base_urls:
      free: "https://apps.microsoft.com/api/Reco/GetComputedProductsList?listName=TopFree&pgNo={page}&noItems=30&filteredCategories=AllProducts&mediaType=apps&gl=CN&hl=zh-cn&exp=-436905532"
      paid: "https://apps.microsoft.com/api/Reco/GetComputedProductsList?listName=topPaid&pgNo={page}&noItems=30&filteredCategories=AllProducts&mediaType=apps&gl=CN&hl=zh-cn&exp=-436905532"
    detail_url: "https://apps.microsoft.com/detail/{app_id}?hl=zh-cn&gl=CN"

  headers:
    common:
      User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      Accept: "application/json"
      Accept-Language: "zh-CN,zh;q=0.9"

  request:
    timeout: 10
    max_retries: 3
    base_delay: 2
    max_delay: 10
    rate_limit_requests: 10
    rate_limit_window: 60

  params:
    max_pages:
      free: 35
      paid: 33

chinaz_web:
  api:
    base_url: "https://top.chinaz.com/all"
  
  headers:
    accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8"
    accept-language: "zh-CN,zh;q=0.9,en;q=0.8"
    user-agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********"
  
  params:
    total_pages: 3503
  
  request:
    timeout: 30
    base_delay: 1
    max_delay: 10
    max_retries: 3

baoku_app:
  api:
    base_url: "https://soft-api.safe.360.cn/main/v1/soft/plists"
  
  headers:
    Accept: "*/*"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    Cache-Control: "no-cache"
    Connection: "keep-alive"
    Pragma: "no-cache"
    Referer: "https://baoku.360.cn/"
    Sec-Fetch-Dest: "script"
    Sec-Fetch-Mode: "no-cors"
    Sec-Fetch-Site: "same-site"
    User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    sec-ch-ua: "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""
    sec-ch-ua-mobile: "?0"
    sec-ch-ua-platform: "\"Windows\""
  
  cookies:
    __guid: "192758817.2533988410917056000.1743039935350.0898"
    __DC_gid: "41272937.261289095.1743040039483.1751612310969.45"
  
  request:
    timeout: 10
    max_retries: 3
    base_delay: 1
    max_delay: 10
    rate_limit_requests: 10
    rate_limit_window: 60
    max_apps_per_category: 200
  
  exclude_names:
    - "手游电脑版"
    - "PDF工具集"
    - "WIN7专区"
    - "XP专区"
    - "安全杀毒"
    - "单机"
    - "回收站"
    - "浏览器"

semrush_web:
  request:
    timeout: 30
    max_retries: 3
    base_delay: 2
    max_delay: 10
    page_load_delay: 2
    click_delay: 1.5

#!/usr/bin/env python
# -*- coding: utf-8 -*-


import re
import os
import time
import hashlib
from multiprocessing import Lock
from pypinyin import lazy_pinyin
from base.log import Logger

logger = Logger()


class AdbTools(object):
    '''
    常用的adb操作　
    '''
    def __init__(self, lock: Lock, device_info: dict, save_path: str):
        self.lock = lock
        self.device = device_info.get("udid")
        self.file_path = save_path
        self.pkg_name = device_info.get('pkg_name')

    def check_display_power_state(self, password=None):
        '''
        检查屏幕状态，并尝试开锁
        :param password: 锁屏密码
        :return:
        '''
        # 熄屏状态检查
        display_power_state = \
            os.popen('/usr/local/share/android-sdk/platform-tools/adb shell dumpsys power | '
                     'grep "Display Power: state=" | awk -F "=" "{print $2}"').read().strip('\n').split('=')[1]
        if display_power_state == 'OFF':
            logger.info('手机处于锁屏状态，正在点亮屏幕...')
            os.system('/usr/local/share/android-sdk/platform-tools/adb shell input keyevent 26')

        # 解锁屏幕
        # 屏幕当前状态
        is_status_barkeyguard = \
            os.popen(' /usr/local/share/android-sdk/platform-tools/adb shell dumpsys window policy|grep mIsShowing |'
                     ' awk -F "=" "{print $3}"').read().strip('\n').split('=')[-1]
        if is_status_barkeyguard == 'true':
            # time.sleep(2)
            logger.info('解锁屏保中...')
            screen_size = self.screen_size()
            if screen_size:
                wigth = int(screen_size[0])
                height = int(screen_size[1])
                x1 = round(wigth * 0.5)
                x2 = round(wigth * 0.5)
                y1 = round(height * 0.9)
                y2 = round(height * 0.3)
                os.system('/usr/local/share/android-sdk/platform-tools/adb shell input swipe'
                          ' {} {} {} {} 100'.format(x1, y1, x2, y2))
                # time.sleep(1)
                # 尝试解锁密码
                if password:
                    os.system('/usr/local/share/android-sdk/platform-tools/adb shell input text %s' % password)
                logger.info('屏幕已解锁完成')

    def install_app(self):
        '''
        安装app到手机中
        :param app_name: app的名称
        :return:
        '''
        logger.info('找到设备[%s]，将开始进行app装操作...' % self.device)
        install_msg = os.popen('adb -s %s install %s' % (self.device, self.file_path)).read()
        if install_msg.find('Success'):
            logger.info('%s 安装成功' % self.pkg_name)
            return True
        else:
            logger.error('安装失败，请检查USB安装选项是否打开；USB安装管理是否关闭！')
            return False

    def screen_size(self):
        '''
        获取手机的屏幕高，宽
        :return: screen_size(wigth, height)
        '''
        screen_size = os.popen('adb shell getevent -p|grep -e "0035" -e "0036"').read()
        try:
            screen_size = re.findall(r'max (\d+)', screen_size)
            if len(screen_size) == 2:
                return screen_size
            else:
                logger.error('未获取到屏幕的宽，高数据')
                exit()
        except Exception as e:
            logger.error(e)

    def package_and_appactivity(self, app_name):
        '''
        获取app的包名和启动名称
        :param app_name: app的名称
        :return: app_launch_info (包名，启动名成)
        '''
        app_info = os.popen('aapt dump badging %s' % app_name).read()
        package_name = re.findall(r'package: name=\'(.+)\' versionCode', app_info)
        activity_name = re.findall(r'launchable-activity: name=\'(.+)\'  label', app_info)
        if package_name and activity_name:
            app_launch_info = (package_name[0], activity_name[0])
            return app_launch_info
        else:
            logger.error('包名或者启动页面名称获取失败')

    def capture(self, meid, timeout=None):
        '''
        开始抓取pcap,tcpdump 需要放在指定路径下：/sdcard/capture
        :param meid:
        :param timeout:
        :return:
        '''
        os.popen('adb -s {} shell "su -c cd /sdcard/capture"'.format(meid))
        if timeout:
            cmd = 'adb -s {} shell "su -c timeout {} tcpdump !icmp -i wlan0 -s 0 ' \
                  '-w /sdcard/capture/capture_temp.pcap"'.format(meid, timeout)
        else:
            cmd = 'adb -s {} shell "su -c tcpdump !icmp -i wlan0 -s 0 ' \
                  '-w /sdcard/capture/capture_temp.pcap"'.format(meid)
        print('start tcpdump')
        os.popen(cmd)
        time.sleep(1)

    def _pid(self, meid, pid_name):
        pattern = re.compile('root(.*?\d+)', re.S)
        p = os.popen('adb -s {} shell "su -c ps|grep {}"'.format(meid, pid_name))  # 获取tcpdump进程pid
        time.sleep(1)
        contents = p.read()
        try:
            result = re.search(pattern, str(contents)).group(1)
            pid = result.strip()
            return pid
        except:
            return 0

    def end_capture_without_save(self, meid):
        pid = self._pid(meid, 'tcpdump')
        if pid != 0:
            kill_cmd = 'adb -s {} shell "su -c kill -s 3 '.format(meid) + pid + '"'
            time.sleep(2)
            os.popen(kill_cmd)  # 结束抓包进程

    def end_capture(self, meid, file_name, timeout=0):
        '''
        停止抓包，并保存pcap文件
        :param meid:
        :param file_name:
        :param timeout:
        :return:
        '''
        file_path, md5_file_name = self.file_info(file_name)

        if timeout == 0:
            pid = self._pid(meid, 'tcpdump')
            print('kill pid:{}...'.format(pid))
            kill_cmd = 'adb -s {} shell "su -c kill -s 3 '.format(meid) + pid + '"'
            time.sleep(2)
            os.popen(kill_cmd)  # 结束抓包进程
        else:
            time.sleep(timeout + 1)
            # init_pid = self._pid(meid, 'tcpdump')
            # print('wait for capture command...')
            # cnt_num = 0
            # while True:
            #     if cnt_num > 4:
            #         break
            #     time.sleep(3)
            #     if self._pid(meid, 'tcpdump') != init_pid:
            #         break
            #     cnt_num += 1
        pull_cmd = 'adb -s {} pull /sdcard/capture/capture_temp.pcap '.format(meid) + '"' + \
                   file_path + '/' + md5_file_name + '.pcap' + '"'
        os.system(pull_cmd)  # 将pcap文件拷贝到电脑
        # time.sleep(3)
        os.popen('adb -s {} shell "cd /sdcard/capture && su -c rm *.pcap"'.format(meid))  # 删除手机内旧的pcap包

    def file_info(self, app_name):
        # 创建保存文件路径
        file_path = '/Users/<USER>/Downloads/capture/'
        time_today = time.strftime('%Y%m%d', time.localtime(time.time()))
        name_pinyin = ''.join(lazy_pinyin(app_name))
        # 判断是否存在 name_pinyin 的文件夹,不存在就创建
        if not os.path.exists(file_path + name_pinyin):
            os.makedirs(file_path + name_pinyin)
        file_path = file_path + name_pinyin
        # 判断是否存在 time_today 的文件夹,不存在就创建
        if not os.path.exists(file_path + '/' + time_today):
            os.makedirs(file_path + '/' + time_today)
        file_path = file_path + '/' + time_today
        # 文件名md5值
        local_time = time.strftime("%Y%m%d%H%M%S", time.localtime())
        file_name = name_pinyin + local_time
        name_md5 = hashlib.md5(file_name.encode(encoding='UTF-8')).hexdigest()
        return file_path, name_md5

    def choose_ime(self):
        '''
        修改输入法
        :return:
        '''
        os.popen('/usr/local/share/android-sdk/platform-tools/adb '
                 'shell settings put secure default_input_method com.sohu.inputmethod.sogou.xiaomi/.SogouIME')


if __name__ == '__main__':
    adbtool = AdbTools()
    adbtool.check_display_power_state()
# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main\\app_analysis.py'],
    pathex=[],
    binaries=[('D:\\workspace\\venv\\py38\\Lib\\site-packages\\poco\\drivers', '.\\poco\\drivers\\'),
              ('D:\\workspace\\venv\\py38\Lib\\site-packages\\airtest', '.\\airtest\\')],
    datas=[("config\\", ".\\config\\"), ("resource\\", ".\\resource\\"), ("plugins\\", ".\\plugins\\")],
    hiddenimports=["airtest", "poco"],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['main\\hook.py'],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='brd-automation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['resource\\image\\logo.ico'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='brd-automation',
)

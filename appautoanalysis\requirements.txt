aiofiles==23.2.1
airtest==1.3.3
airtest-selenium==1.0.4
altgraph==0.17.4
annotated-types==0.6.0
ansicon==1.89.0
anyio==4.3.0
appdirs==1.4.4
Appium-Python-Client==2.9.0
APScheduler==3.9.1
arrow==1.3.0
asn1==2.7.0
asttokens==2.4.1
async-generator==1.10
async-timeout==4.0.3
attrs==23.1.0
backcall==0.2.0
backports.zoneinfo==0.2.1
bcrypt==4.0.1
blessed==1.20.0
bpylist2==4.1.1
cached-property==1.5.2
certifi==2022.12.7
cffi==1.15.1
charset-normalizer==3.1.0
click==8.1.7
colorama==0.4.6
colored==1.4.4
coloredlogs==15.0.1
comtypes==1.2.1
concurrent-log-handler==0.9.24
construct==2.10.70
contourpy==1.1.1
cryptography==42.0.4
cycler==0.12.1
daemonize==2.5.0
decorator==4.4.2
Deprecated==1.2.14
deprecation==2.1.0
developer-disk-image==0.0.2
dnspython==2.6.1
dpkt==1.9.2
email-validator==2.1.0.post1
enum-compat==0.0.3
et-xmlfile==1.1.0
exceptiongroup==1.1.1
executing==2.0.1
facebook-wda==1.4.6
fastapi==0.109.2
ffmpeg-python==0.2.0
filelock==3.13.1
flatbuffers==23.5.26
fonttools==4.44.0
fsspec==2023.10.0
future==0.18.3
gpxpy==1.5.0
h11==0.14.0
hexdump==3.3
hrpc==1.0.9
httpcore==1.0.4
httptools==0.6.1
httpx==0.27.0
humanfriendly==10.0
hyperframe==6.0.1
idna==3.4
ifaddr==0.2.0
imageio==2.32.0
imageio-ffmpeg==0.4.9
importlib-metadata==7.0.0
importlib-resources==6.1.1
imutils==0.5.4
inquirer3==0.4.0
ipsw-parser==1.2.1
ipython==8.12.3
itsdangerous==2.1.2
jedi==0.19.1
Jinja2==3.1.2
jinxed==1.2.1
joblib==1.3.2
kiwisolver==1.4.5
la-panic==0.4.9
lazy_loader==0.3
logzero==1.7.0
lxml==4.9.2
mahotas==1.4.13
MarkupSafe==2.1.3
matplotlib==3.7.3
matplotlib-inline==0.1.6
mpmath==1.3.0
mss==6.1.0
nest-asyncio==1.6.0
networkx==3.1
numpy==1.24.3
opack==0.1.0
opencv-contrib-python==********
openpyxl==3.1.2
orjson==3.9.14
outcome==1.2.0
packaging==23.1
pandas==2.0.1
parameter-decorators==0.0.2
paramiko==3.3.1
parso==0.8.3
pefile==2023.2.7
pickleshare==0.7.5
Pillow==10.0.1
plumbum==1.8.2
pocoui==1.0.94
portalocker==2.7.0
proglog==0.1.10
prompt-toolkit==3.0.43
protobuf==4.25.0
psutil==5.9.7
pure-eval==0.2.2
py==1.11.0
pycparser==2.21
pycrashreport==1.2.2
pycryptodome==3.20.0
pydantic==2.6.1
pydantic-extra-types==2.5.0
pydantic-settings==2.2.1
pydantic_core==2.16.2
Pygments==2.17.2
pygnuutils==0.1.1
pyimg4==0.8
pyinstaller==6.3.0
pyinstaller-hooks-contrib==2023.10
pykdebugparser==1.2.4
pymobiledevice3==2.43.8
PyMySQL==1.0.3
PyNaCl==1.5.0
pyOpenSSL==24.0.0
pyparsing==3.1.1
pypinyin==0.48.0
pypiwin32==223
pyreadline3==3.4.1
pyshark==0.6
PySocks==1.7.1
python-dateutil==2.8.2
python-dotenv==1.0.1
python-editor==1.0.4
python-multipart==0.0.9
pytz==2023.3
pyusb==1.2.1
PyWavelets==1.4.1
pywin32==306
pywin32-ctypes==0.2.2
pywinauto==0.6.3
pywintunx-pmd3==1.0.2
PyYAML==6.0
qh3==0.15.0
readchar==4.0.5
remotezip==0.12.2
requests==2.30.0
retry==0.9.2
scapy==2.5.0
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.1
selenium==4.9.0
simple-tornado==0.2.2
simplejson==3.19.2
six==1.16.0
sniffio==1.3.0
sortedcontainers==2.4.0
srptools==1.0.1
sslpsk-pmd3==1.0.3
stack-data==0.6.3
starlette==0.36.3
sympy==1.12
tabulate==0.9.0
termcolor==2.3.0
threadpoolctl==3.2.0
tidevice==0.12.6
tifffile==2023.7.10
torch==2.1.0
torchvision==0.16.0
tornado==6.4
tqdm==4.66.1
traitlets==5.14.1
trio==0.22.0
trio-websocket==0.10.2
types-python-dateutil==2.8.19.20240106
typing_extensions==4.8.0
tzdata==2023.3
tzlocal==5.0.1
ujson==5.9.0
urllib3==1.26.15
uvicorn==0.27.1
watchfiles==0.21.0
wcwidth==0.2.13
websocket-client==0.48.0
websockets==12.0
wrapt==1.16.0
wsproto==1.2.0
xonsh==0.14.0
zeroconf==0.131.0
zipp==3.17.0

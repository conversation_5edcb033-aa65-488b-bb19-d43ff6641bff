# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   goudaow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   wx_logo_detect.py
@Time    :   2023/11/20 16:40
"""
import os

import cv2
import numpy as np
import matplotlib.pyplot as plt
from hash_similar import DuplicateFiles
import time

logo = r'D:\workspace\appautoanalysis\resource\login\6.png'
login = r"D:\workspace\appautoanalysis\resource\screenshot\6040-2023-11-14-143516.png"


def show_bboxes(image, bboxes):
    img_h, img_w = image.shape[:2]
    figsize = (int(img_h/(img_h + img_w)*20), int(img_w/(img_h + img_w)*20))
    img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    for bbox in bboxes:
        x, y, w, h = bbox
        cv2.rectangle(img_rgb, (x, y), (x+w, y+h), color=(255, 0, 0), thickness=2)
    plt.figure(figsize=figsize)
    plt.imshow(img_rgb)
    plt.axis('off')
    plt.show()


def contours_detect(image, index, threshold_rate=0.02):
    img_h, img_w = image.shape[:2]
    gary_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    ret, otsu = cv2.threshold(gary_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    threshold_min = ret
    threshold_max = ret + (255 - ret) // 2
    edge = cv2.Canny(image, threshold_min, threshold_max)
    # gray = cv2.cvtColor(edge, cv2.COLOR_BGR2GRAY)
    # ret, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    # contours, hierarchy = cv2.findContours(edge, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    contours, hierarchy = cv2.findContours(edge, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
    boundingboxes = [cv2.boundingRect(c) for c in contours]
    filtered_bboxes = [bbox for bbox in boundingboxes if
                       (bbox[-2] > img_w * threshold_rate) and (bbox[-1] > img_h * threshold_rate)]

    wx_logo = r"D:\workspace\appautoanalysis\resource\login\1.png"
    # img1 = cv2.imdecode(np.fromfile(wx_logo, dtype=np.uint8), cv2.IMREAD_COLOR)
    img1 = cv2.imread(wx_logo)

    for index2, box in enumerate(filtered_bboxes):
        x, y, w, h = box
        im = image[y:y+h, x:x+w]
        tmp = r"D:\workspace\appautoanalysis\resource\tmp"
        tmp_file = os.path.join(tmp, f"{index}-{index2}.png")
        try:
            cv2.imwrite(tmp_file, im)
            # img2 = cv2.imdecode(np.fromfile(tmp_file, dtype=np.uint8), cv2.IMREAD_COLOR)
            # df.detect_wx_logo(img1, img2)
            # img2 = cv2.imread(tmp_file)
            # print(cv2.compare_ssim(img1, img2, multichannel=True))
        except Exception as e:
            print(e)
    return filtered_bboxes


def test():
    df = DuplicateFiles(r"D:\workspace\appautoanalysis\resource\login_df")
    shot_screen_dir = r"D:\workspace\appautoanalysis\resource\screenshot"
    images = df.list_all_files(shot_screen_dir)
    for index, item in enumerate(images):
        image = cv2.imread(item)
        boxes = contours_detect(image, index=index)
        show_bboxes(image, boxes)


if __name__ == "__main__":
    test()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import yaml
import os
import random
from lxml import etree

# https://github.com/fex-team/ua-device/tree/master
# http://useragentstring.com/


def html_content(url, header=None, cookies=None, refer=None):
    '''
    发起html页面请求，并返回原始 response
    :param url: 请求的host地址
    :return: 返回原始的页面响应数据
    '''
    raw_rest = None
    if not header.get('User-Agent'):
        headers = {'User-Agent': user_agent()}
    if header:
        for k, v in header.items():
            headers[k] = v
    if refer:
        headers['Referer'] = refer
    if cookies:
        rest = requests.get(url, headers=headers, cookies=cookies)
    else:
        rest = requests.get(url, headers=headers)
    encode = rest.apparent_encoding
    cookies = rest.cookies
    cookie = requests.utils.dict_from_cookiejar(cookies)
    if rest.status_code == 200:
        raw_rest = rest.content
        return raw_rest, encode, cookie


def complete_host(host, tls=0):
    '''
    按照'protocol://domain/'的格式格式化host
    :param host: 需要处理的host
    :param tls: host是否采用https，0：https 1：http
    :return: 格式化后的host
    '''
    if '://' not in host:
        if tls == 0:
            host = 'https://' + host
        else:
            host = 'http://' + host
        return host
    else:
        return host


def user_agent():
    '''
    随机返回一个useragentntu
    :return: useragent
    '''
    yaml_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config/ua.yaml')
    ua_conf = open(yaml_file, encoding='utf-8').read()
    user_agents = yaml.load(ua_conf, Loader=yaml.FullLoader)
    user_agents = user_agents.get('userAgent')

    return random.choice(user_agents)


def x_tree(content):
    tree = etree.HTML(content)

    return tree


if __name__ == '__main__':
    print(_headers())

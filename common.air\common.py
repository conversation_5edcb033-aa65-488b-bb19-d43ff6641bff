# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   common.py
@Time    :   2024/1/16 10:40
"""
import datetime
import paramiko
import json
import os
import time
from airtest.core.api import sleep, init_device, connect_device, Template, TargetNotFoundError, wait, touch, exists
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from poco.exceptions import PocoTargetTimeout
from poco.drivers.ios import iosPoco


class DeviceDriver(object):
    def __init__(self, device_info: dict, logdir: str):
        self.driver = None
        self.poco = None
        self.device_info = device_info
        self.scenes = list()
        self.logdir = logdir
        self._create_logdir(self.logdir)
        self.connect()

    def _create_logdir(self, path: str):
        """创建日志目录"""
        try:
            if not os.path.exists(path):
                self._create_logdir(os.path.split(path)[0])
            else:
                return
            os.mkdir(path)
        except Exception as e:
            print(f"创建目录失败-{str(e)}")

    def connect(self) -> bool:
        """连接设备"""
        if self.device_info.get("platform") == "Android":
            try:
                self.driver = init_device(platform="Android", uuid=self.device_info.get("uuid"), cap_method="JAVACAP")
                self.poco = AndroidUiautomationPoco(self.driver)
                return True
            except Exception as e:
                print("设备连接失败")
        if self.device_info.get("platform") == "IOS":
            if self.device_info.get('uri'):
                try:
                    self.driver = connect_device(self.device_info.get('uri'))
                    self.poco = iosPoco(self.driver)
                    return True
                except Exception as e:
                    print("设备连接失败")
            else:
                try:
                    self.driver = init_device(platform="IOS", uuid=self.device_info.get("uuid"))
                    self.poco = iosPoco(self.driver)
                    return True
                except Exception as e:
                    print("设备连接失败")
        return False

    def start_app(self, package: str):
        """
        启动app
        :param package: 应用包名
        :return:
        """
        try:
            self.driver.start_app(package)
        except Exception as e:
            raise InterruptedError("包名异常")

    def snapshot(self, message: str = ""):
        """
        自定义截图
        :param message: 截图名字
        :return:
        """
        current_time = datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')
        if message:
            filename = 'brd-' + message + f'-{current_time}.jpg'
        else:
            filename = f"brd-{current_time}.jpg"
        self.driver.snapshot(os.path.join(self.logdir, filename))

    def assert_wait(self, target: Template or str, timeout: int = 120) -> bool:
        """
        等待指定的目标(图片或text文本)出现，在超时时间内出现返回true，否则返回false
        :param target: 图片对象
        :param timeout: 超时时间
        :return: bool
        """
        if isinstance(target, Template):
            try:
                wait(target, timeout)
                self.snapshot()
                return True
            except TargetNotFoundError:
                self.snapshot("阻断失败")
                return False
        if isinstance(target, str):
            try:
                self.poco(textMatches=f".*{target}.*").wait_for_appearance(timeout=timeout)
                self.snapshot()
                return True
            except PocoTargetTimeout:
                return False
        return False

    def poco_click(self, **kwargs):
        """
        使用poco控件元素进行点击
        :param kwargs: text="xxx", 正则写法：textMatches=".*xxx.*"
        :return:
        """
        if self.poco(**kwargs).wait(timeout=20).exists():
            self.poco(**kwargs).click()

    def poco_input(self, text_value: str, **kwargs):
        """
        使用poco控件元素进行输入
        :param text_value: 文本
        :param kwargs: text="xxx", 正则写法：textMatches=".*xxx.*"
        :return:
        """
        try:
            if self.device_info.get("platform") == "Android":
                self.poco(**kwargs).set_text(text_value)
            if self.device_info.get("platform") == "IOS":
                self.text(text_value)
        except Exception as e:
            raise InterruptedError("输入文本失败")

    def poco_quic(self, text_value: str, **kwargs):
        """
        使用poco控件元素进行点击和输入，用于设置用户名和密码等
        :param text_value: 文本
        :param kwargs: text="xxx", 正则写法：textMatches=".*xxx.*"
        :return:
        """
        self.poco_click(**kwargs)
        self.poco_input(text_value, **kwargs)

    def continuously_exists(self, target: Template or str, timeout: int = 180) -> bool:
        """
        指定目标是否持续存在，如果持续存在（在超时时间内）返回True，否则返回False
        :param target: 图片对象
        :param timeout: 超时时间
        :return: bool
        """
        start_time = time.time()
        if isinstance(target, Template):
            while True:
                if time.time() - start_time >= timeout:
                    self.snapshot("持续出现")
                    return True
                sleep(1)
                flag = exists(target)
                if not flag:
                    return False
        if isinstance(target, str):
            while True:
                if time.time() - start_time >= timeout:
                    self.snapshot("持续出现")
                    return True
                sleep(1)
                flag = self.poco(textMatches=f".*{target}.*").exists()
                if not flag:
                    return False
        return False

    def pclick(self, *args, timeout: int = 60, **kwargs):
        """
        在超时时间内点击传入的可能存在的目标,传入的可以是多张图片和多个poco控件
        :param args: 图片对象
        :param timeout: 超时时间
        :param kwargs: poco元素控件，如：text="允许"
        :return:
        """
        start_time = time.time()
        while time.time() - start_time <= timeout:
            for item in args:
                if exists(item):
                    self.click(item)
            self.sleep(1)
            for key, value in kwargs.items():
                if self.poco(key=value).exists():
                    self.poco(key=value).click()
            self.sleep(1)

    def click(self, target: Template or tuple, timeout: int = 20):
        """
        如果存在指定的目标，则进行点击；target也可以是坐标，可用于关闭弹窗（如广告等）
        :param target: 图片对象
        :param timeout: 超时时间
        :return:
        """
        if isinstance(target, Template):
            try:
                wait(target, timeout)
                touch(target)
            except TargetNotFoundError:
                pass
        if isinstance(target, tuple):
            touch(target)

    def text(self, text: str, enter: bool = True, **kwargs):
        """
        输入文本
        :param text: 文本内容
        :param enter: 是否在输入完毕后，执行一次Enter，默认是True
        :param kwargs: 在Android上，有时需要在输入完毕后点击搜索按钮，search=True
        :return:
        """
        try:
            self.driver.text(text, enter=enter, **kwargs)
        except Exception as e:
            raise InterruptedError("输入文本失败")

    def swipe(self, x_proportion: tuple = (0.9, 0.1), y_proportion: tuple = (0.5, 0.5),
              direction: str = 'custom', duration: int = 1):
        """
        上下左右滑动，同时支持自定义滑动(ps: 手机左上角为坐标原点)
        :param x_proportion: 横坐标滑动比例（起始位置和结束位置比例）
        :param y_proportion: 纵坐标滑动比例（起始位置和结束位置比例）
        :param direction: 滑动方向（left、right、up、down、custom（自定义））
        :param duration: 滑动持续时间
        :return:
        """
        if self.device_info.get("platform") == "Android":
            dev_info = self.driver.get_display_info()
            width, height, orientation = dev_info.get("width"), dev_info.get("height"), dev_info.get("orientation")
            if orientation == 1:
                # 横屏
                width, height = height, width
        elif self.device_info.get("platform") == "IOS":
            dev_info = self.driver.display_info
            width, height, orientation = dev_info.get("width"), dev_info.get("height"), dev_info.get("orientation")
            if orientation != 'PORTRAIT':
                # 横屏
                width, height = height, width
        else:
            return
        if direction == 'custom':
            fpos = (int(width * x_proportion[0]), int(height * y_proportion[0]))
            tpos = (int(width * x_proportion[1]), int(height * y_proportion[1]))
        elif direction == 'left':
            fpos = (int(width * 0.9), int(height * 0.5))
            tpos = (int(width * 0.1), int(height * 0.5))
        elif direction == 'right':
            fpos = (int(width * 0.1), int(height * 0.5))
            tpos = (int(width * 0.9), int(height * 0.5))
        elif direction == 'up':
            fpos = (int(width * 0.5), int(height * 0.9))
            tpos = (int(width * 0.5), int(height * 0.1))
        elif direction == 'down':
            fpos = (int(width * 0.5), int(height * 0.2))
            tpos = (int(width * 0.5), int(height * 0.9))
        else:
            return
        if self.device_info.get("platform") == "IOS":
            self.driver.swipe(fpos, tpos, duration=duration)
        if self.device_info.get("platform") == "Android":
            self.driver.swipe(fpos, tpos, duration=duration)

    def add(self, target: Template or str, timeout: int = 180, continuous: bool = False, msg: str = "未出现指定目标"):
        """
        阻断场景判断
        :param target: 等待是否出现指定目标（图片/文本）或者是否持续出现指定的目标（图片/文本）
        :param timeout: 超时时间
        :param continuous: 是否持续出现
        :param msg: 页面阻断失败描述
        :return:
        """
        if continuous:
            flag = self.continuously_exists(target, timeout)
        else:
            flag = self.assert_wait(target, timeout)
        if not flag:
            raise InterruptedError(msg)
        self.scenes.append(flag)

    def padd(self, *args, timeout: int = 180, msg: str = "未出现多场景目标"):
        """
        多个阻断场景判断，用于同一个页面可能出现的多个阻断提示
        :param args: 多个阻断场景
        :param timeout: 超时时间
        :param msg: 页面阻断失败描述
        :return:
        """
        start_time = time.time()
        while time.time() - start_time <= timeout:
            for item in args:
                if exists(item):
                    self.scenes.append(True)
                    self.snapshot()
                    return
            time.sleep(0.5)
        raise InterruptedError(msg)

    def cadd(self, blank: Template, blocked: Template, timeout: int = 180, msg: str = "未一直白板页面"):
        """
        在超时时间内，一直是'白板页面'或者出现了指定的明显的阻断页面，算阻断
        :param blank: 白板页面
        :param blocked: 明显的阻断提示页面
        :param timeout: 超时时间
        :param msg: 页面阻断失败描述
        :return:
        """
        block = False
        start_time = time.time()
        while time.time() - start_time <= timeout:
            if exists(blocked):
                block = True
                break
            else:
                if exists(blank):
                    block = True
                else:
                    block = False
                    break
            time.sleep(1)
        if block:
            self.snapshot()
        else:
            raise InterruptedError(msg)
        self.scenes.append(block)

    def keyevent(self, keyname, **kwargs):
        """
        事件方法
        :param keyname: HOME/POWER/MENU/BACK
        :return:
        """
        self.driver.keyevent(keyname, **kwargs)

    def back(self):
        """
        返回
        :return:
        """
        self.keyevent("BACK")

    @staticmethod
    def sleep(sleep_time: int):
        sleep(sleep_time)

    def blocked(self) -> bool:
        """
        是否所有场景都阻断
        :return:
        """
        tmp_scenes = set(self.scenes)
        if False in tmp_scenes or len(tmp_scenes) == 0:
            return False
        return True

    def clear_redundant(self):
        """清理多余图片"""
        try:
            file_list = os.listdir(self.logdir)
            for file_name in file_list:
                file_path = os.path.join(self.logdir, file_name)
                if os.path.exists(file_path) and file_name.endswith('.jpg') and not file_name.startswith('brd-'):
                    os.remove(file_path)
        except OSError as e:
            print(e)

    class Router(object):

        def __init__(self, device_info: dict):
            self.ssh = None
            self.device_info = device_info

        def connect(self) -> bool:
            try:
                self.ssh = paramiko.SSHClient()
                self.ssh.set_missing_host_key_policy(paramiko.WarningPolicy())
                self.ssh.connect(self.device_info["router_host"], self.device_info["router_port"],
                                 self.device_info["router_user"], self.device_info["router_pwd"], timeout=30)
                return True
            except Exception as e:
                print(e)
                return False

        def exe_invoke_shell(self, appid: int, clear: int = 0) -> bool:
            try:
                router_index = self.device_info.get("router_index")
                chan = self.ssh.invoke_shell()
                chan.settimeout(10)
                chan.send('enable' + '\n')
                time.sleep(1)
                chan.send(self.device_info["router_enable_pwd"] + '\n')
                time.sleep(1)
                chan.send('config' + '\n')
                time.sleep(1)
                chan.send(f'ip access-list extended {self.device_info.get("extend_device")}' + '\n')
                time.sleep(1)
                if clear:
                    cmd = f"no deny ip any any si-appid {appid}"
                    chan.send(cmd + '\n')
                    time.sleep(1)
                else:
                    cmd = f"no {router_index}"
                    chan.send(cmd + '\n')
                    time.sleep(1)
                    chan.send(f'{router_index} deny ip any any si-appid {appid}' + '\n')
                    time.sleep(1)
                chan.send('exit' + '\n')
                chan.send('exit' + '\n')
                return True
            except Exception as e:
                print(e)
                return False

        def close(self):
            if self.ssh:
                try:
                    self.ssh.close()
                except Exception as e:
                    pass

    def rule_handle(self, dev_router: dict, l_class: int, clear: int = 0, timeout: int = 15) -> bool:
        """
        阻断规则下发与取消
        :param dev_router: 阻断路由器配置
        :param l_class: 小类ID
        :param clear: 0-阻断；1-取消阻断
        :param timeout: 下发等待生效时间
        :return: 规则是否下发成功
        """
        router = self.Router(dev_router)
        if not router.connect():
            return False
        flag = router.exe_invoke_shell(l_class, clear)
        router.close()
        time.sleep(timeout)
        return flag

    def account(self) -> dict:
        """公用的账号"""
        path = os.path.dirname(os.path.abspath(__file__))
        with open(os.path.join(path, 'account.json'), "r", encoding="utf-8") as f:
            return json.load(f)





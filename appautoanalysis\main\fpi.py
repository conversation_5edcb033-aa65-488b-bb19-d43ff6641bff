# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   fpi.py
@Time    :   2023/6/30 13:32
"""
import os
import csv
import re
from xml.dom import minidom
import platform
import asyncio
import pyshark

system = platform.system()
apps_path = r"D:\code\appautoanalysis\config\apps_data.csv"
result_path = r"D:\log\analysis_result-2023-06-26-133910.csv"
tshark = r"D:\Program Files\Wireshark\tshark.exe"
fpi_path = r"D:\log\apps_result.csv"
feature_path = r"D:\code\appautoanalysis\config\feature.xml"
template = r"D:\code\appautoanalysis\config\template.xml"
LF = "\n" if system == "Linux" else "\r\n"


class FileParsing(object):
    def __init__(self):
        self.attribute = dict()
        self.init_template()
        self.sniffer_template = self.attribute["sniffer"]

    def init_template(self):
        # """打开选择并读取文件"""
        """获取模板文件路径"""
        try:
            if template:
                """读取模板文件并作为列表返回文件中的所有行"""
                with open(template, 'r', encoding='utf-8') as f:
                    attribute_list = f.readlines()
                """创建临时字典存储数据"""
                tmp_dict = dict()
                for i in range(0, len(attribute_list)-2):
                    if 'snifferCfg' in attribute_list[i]:
                        continue
                    elif 'snf_version' in attribute_list[i]:
                        snf_list = re.findall(r' (.*?)="', attribute_list[i])
                        snf_value_list = dict([(key, "") for key in snf_list])
                        self.attribute['snf_version'] = {'property': snf_value_list, 'type': 0}
                    elif 'cfg_item' in attribute_list[i]:
                        cfg_list = re.findall(r' (.*?)="', attribute_list[i])
                        cfg_value_list = dict([(key, "") for key in cfg_list])
                        self.attribute['cfg_item'] = {'property': cfg_value_list, 'type': 0}
                    elif 'sniffer ' in attribute_list[i]:
                        main_list = re.findall(r' (.*?)="', attribute_list[i])
                        main_value_list = dict([(key, "") for key in main_list])
                        tmp_dict['property'] = main_value_list
                    else:
                        list1 = re.findall(r' (.*?)="', attribute_list[i])
                        value_list = dict([(key, "") for key in list1])
                        if "</" in attribute_list[i]:
                            keys = re.findall(r'<(.*?)>', attribute_list[i])
                            if keys:
                                key = keys[0]
                                if " " in key:
                                    key = key.split(' ')[0]
                                tmp_dict[key] = {"type": 1, "property": value_list, "value": ""}
                        else:
                            keys = re.findall(r'<(.*?) ', attribute_list[i])
                            if keys:
                                key = keys[0]
                                tmp_dict[key] = {"type": 0, "property": value_list}
                self.attribute['sniffer'] = tmp_dict
        except Exception as e:
            print("解析特征库模板文件异常")

    def read_xml(self, file_path: str):
        result_sniffer = dict()
        try:
            domTree = minidom.parse(file_path)
            # 文档根元素
            rootNode = domTree.documentElement
            """所有规则体"""
            sniffers = rootNode.getElementsByTagName("sniffer")
            sniffer_list = list()
            """遍历每个规则体属性"""
            for sniffer in sniffers:
                """创建一个字典保存每个规则体属性"""
                sniffer_attr = dict()
                property_attr = dict()
                for k in self.sniffer_template.keys():
                    if k == "property":
                        for key in self.sniffer_template[k].keys():
                            if sniffer.hasAttribute(key):
                                property_attr[key] = sniffer.getAttribute(key)
                            else:
                                continue
                        sniffer_attr[k] = property_attr
                    else:
                        results = sniffer.getElementsByTagName(k)
                        if results:
                            for result in results:
                                tmp_attr = dict()
                                for key in self.sniffer_template[k]["property"].keys():
                                    if result.hasAttribute(key):
                                        tmp_attr[key] = result.getAttribute(key)
                                    else:
                                        continue
                                if self.sniffer_template[k]["type"] == 1:
                                    if result.childNodes:
                                        value = result.childNodes[0].data
                                        tmp_attr["_text"] = value
                                if k in sniffer_attr.keys():
                                    sniffer_attr[k]["property"].append(tmp_attr)
                                else:
                                    sniffer_attr[k] = dict()
                                    sniffer_attr[k]["property"] = [tmp_attr]
                                    sniffer_attr[k]["type"] = self.sniffer_template[k]["type"]
                sniffer_list.append(sniffer_attr)
            result_sniffer["sniffers"] = sniffer_list
        except Exception as e:
            print("解析特征库文件异常")
        return result_sniffer


def get_apps():
    apps = list()
    with open(apps_path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            apps.append(row)
    return apps


def processed():
    names = []
    if not os.path.exists(fpi_path):
        return names
    with open(fpi_path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            names.append(row[0])
    return names


def get_result():
    apps_dict = {}
    with open(result_path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            name = row[0]
            apps_dict[name] = row
    return apps_dict


def write_result(data: list):
    header = ["小类名称", "小类英文名称", "小类ID", "大类名称", "大类英文名称", "大类ID", "Ntop", "FPI"]
    if not os.path.exists(fpi_path):
        with open(fpi_path, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
    with open(fpi_path, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        writer.writerow(data)


def _find_sniffer(sniffers: dict, name: str) -> dict:
    """查找特征节点"""
    for sniffer in sniffers.get("sniffers", []):
        feature_name = sniffer.get("property", {}).get("name")
        if feature_name == name:
            return sniffer
    return {}


def get_packet_data(pcap: str):
    if system == "Linux":
        loop = None
    else:
        loop = asyncio.ProactorEventLoop()
        asyncio.set_event_loop(loop)
    packets = pyshark.FileCapture(pcap, tshark_path=tshark, display_filter='http or tls or dns', eventloop=loop)
    packets.load_packets()
    dnss = {}
    compare = []
    for packet in packets:
        highest_layer = packet.highest_layer
        if highest_layer == "DNS":
            if hasattr(packet.dns, 'resp_name') and hasattr(packet.dns, "a"):
                if packet.dns.a not in dnss:
                    dnss[packet.dns.a] = packet.dns.resp_name
        if highest_layer == "TLS" and hasattr(packet[highest_layer], 'handshake_extensions_server_name'):
            server_name = str(packet[highest_layer].handshake_extensions_server_name).replace(LF, "")
            data = {"dst": packet.ip.dst, "src": packet.ip.src, "server": server_name}
            compare.append(data)
        if highest_layer == "JSON" and "HTTP" in packet:
            highest_layer = "HTTP"
        if highest_layer == "HTTP":
            if hasattr(packet[highest_layer], 'host'):
                host = str(packet[highest_layer].host)
                data = {"dst": packet.ip.dst, "src": packet.ip.src, "server": host}
                compare.append(data)
    return dnss, compare


def _current_feature(sniffer: dict, server: str):
    strings = sniffer.get("string", {}).get("property", [])
    for item in strings:
        if "host" in item.get("source", "") or "server_name" in item.get("source", ""):
            link = item.get("_text")
            if link and link in server:
                return True
    return False


def check(pcap: str, sniffer: dict):
    dnss, compare = get_packet_data(pcap)
    if not dnss:
        return False
    for item in compare:
        if item.get("dst") in dnss or item.get("src") in dnss:
            server = item["server"]
            if _current_feature(sniffer, server):
                return True
    return False


def check_fpi():
    apps = get_apps()
    apps_dict = get_result()
    names = processed()
    feature_parser = FileParsing()
    sniffers = feature_parser.read_xml(feature_path)
    for app in apps:
        name = app[0]
        if name in names:
            continue
        app_info = apps_dict.get(name)
        if app_info and app_info[6] == "pass":
            pcap = app_info[5]
            if not os.path.exists(pcap):
                pcap = pcap.replace(".eth.pcap", ".pcap")
            sniffer = _find_sniffer(sniffers, name)
            if check(pcap, sniffer):
                app.append("1")
        write_result(app)


if __name__ == "__main__":
    check_fpi()


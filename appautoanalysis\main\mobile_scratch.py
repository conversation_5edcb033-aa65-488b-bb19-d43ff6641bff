# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   mobile_scratch.py
@Time    :   2024/6/13 13:53
"""
import os
import csv
import requests
import json
import threading


headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    "Referer": "http://surfing.tydevice.com/",
    "Origin": "http://surfing.tydevice.com",
    "Content-Type": "application/json;charset=UTF-8",
    "Accept": "application/json, text/plain, */*",
    # "Cookie": "JSESSIONID=BC9FDBA0CE4AD14DA0DB0DAD99DE7895",
    "Accept-Encoding": "gzip, deflate",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Host": "surfing.tydevice.com"
}

file1 = r"E:\tmp\data\brand\shouji.csv"
file2 = r"E:\tmp\data\brand\zhineng.csv"
file3 = r"E:\tmp\data\brand\zhihui.csv"
file4 = r"E:\tmp\data\brand\shuju.csv"
file5 = r"E:\tmp\data\brand\chezai.csv"
file6 = r"E:\tmp\data\brand\weixing.csv"
file7 = r"E:\tmp\data\brand\qita.csv"
file8 = r"E:\tmp\data\brand\yuanqi.csv"

row_head = ["proId", "proModel", "proRegNo", "proName", "isMobile", "propagateName", "proTypeName", "proTypePName"]

detail_url = "http://surfing.tydevice.com/proxyApi/pc/detail/getDetailInfo"


def read_csv(file_path: str) -> list:
    if not os.path.exists(file_path):
        with open(file_path, 'w', encoding="UTF-8-sig", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(row_head)
        return []
    pro_ids = list()
    with open(file_path, 'r', encoding="UTF-8-sig") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            pro_ids.append(row[0])
    return pro_ids


def write_valid(file_path: str, data: list):
    """写入有效的app"""
    with open(file_path, 'a+', encoding="UTF-8-sig", newline='') as f:
        writer = csv.writer(f)
        for row in data:
            writer.writerow(row)


def func1(data_list: list):
    pro_ids = read_csv(file1)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        if index in [0, 1, 2]:
            continue
        print(f"手机终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append([pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name, pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file1, rows)
        print(response.status_code)


def func2(data_list: list):
    pro_ids = read_csv(file2)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"智能穿戴设备: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file2, rows)
        print(response.status_code)


def func3(data_list: list):
    pro_ids = read_csv(file3)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"智慧家庭终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file3, rows)
        print(response.status_code)


def func4(data_list: list):
    pro_ids = read_csv(file4)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"数据终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file4, rows)
        print(response.status_code)


def func5(data_list: list):
    pro_ids = read_csv(file5)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"车载终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file5, rows)
        print(response.status_code)


def func6(data_list: list):
    pro_ids = read_csv(file6)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"卫星终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file6, rows)
        print(response.status_code)


def func7(data_list: list):
    pro_ids = read_csv(file7)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"其它创新终端: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file7, rows)
        print(response.status_code)


def func8(data_list: list):
    pro_ids = read_csv(file8)
    lg = len(data_list)
    for index, item in enumerate(data_list):
        print(f"元器件: {index+1}/{lg}")
        pro_type_id = item.get("proTypeId", "")
        url = "http://surfing.tydevice.com/proxyApi/pc/classify/getProductListByPage"
        data = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": 1, "dynParams": []}
        response = requests.post(url=url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            payload = json.loads(response.text)
            total_count = payload.get("data", {}).get("totalCount", 0)
            if total_count == 0:
                continue
            if total_count % 10 != 0:
                total_pages = int(total_count / 10) + 1
            else:
                total_pages = int(total_count / 10)
            for page in range(total_pages):
                data2 = {"proType": pro_type_id, "keyWord": "", "limit": 10, "page": page + 1, "dynParams": []}
                response2 = requests.post(url=url, headers=headers, data=json.dumps(data2))
                if response2.status_code == 200:
                    payload2 = json.loads(response2.text)
                    rows = list()
                    for info in payload2.get("data", {}).get("list", []):
                        pro_id = str(info.get("proId", ""))
                        if pro_id:
                            params = {"productId": pro_id}
                            response3 = requests.get(url=detail_url, headers=headers, params=params)
                            pro_pagate_name = info.get("propagateName", "")
                            pro_type_name = info.get("proTypeName", "")
                            pro_type_p_name = info.get("proTypePName", "")
                            pro_model = info.get("proModel", "")
                            pro_reg_no = info.get("proRegNo", "")
                            pro_name = info.get("proName", "")
                            is_mobile = info.get("isMobile", "")
                            if response3.status_code == 200:
                                payload3 = json.loads(response3.text)
                                _info = payload3.get("data", {})
                                pro_pagate_name = _info.get("propagateName", "")
                                pro_type_name = _info.get("proTypeName", "")
                                pro_type_p_name = _info.get("proTypePName", "")
                                pro_model = _info.get("proModel", "")
                                pro_reg_no = _info.get("proRegNo", "")
                                pro_name = _info.get("proName", "")
                                is_mobile = _info.get("isMobile", "")
                            if pro_id and pro_id not in pro_ids:
                                rows.append(
                                    [pro_id, pro_model, pro_reg_no, pro_name, is_mobile, pro_pagate_name, pro_type_name,
                                     pro_type_p_name])
                                pro_ids.append(pro_id)
                    if rows:
                        write_valid(file8, rows)
        print(response.status_code)


def sidebar():
    url = "http://surfing.tydevice.com/proxyApi/pc/home/<USER>"
    response = requests.get(url, headers=headers)
    data = json.loads(response.text)
    m1 = "手机终端"
    m2 = "智能穿戴设备"
    m3 = "智慧家庭终端"
    m4 = "数据终端"
    m5 = "车载终端"
    m6 = "卫星终端"
    m7 = "其它创新终端"
    m8 = "元器件"
    threads = []
    for item in data.get("data", []):
        pro_type_name = item.get("proTypeName")
        if pro_type_name == m1:
            t1 = threading.Thread(target=func1, args=(item.get("childNode", []), ))
            t1.start()
            threads.append(t1)
        # if pro_type_name == m2:
        #     t2 = threading.Thread(target=func2, args=(item.get("childNode", []), ))
        #     t2.start()
        #     threads.append(t2)
        # if pro_type_name == m3:
        #     t3 = threading.Thread(target=func3, args=(item.get("childNode", []), ))
        #     t3.start()
        #     threads.append(t3)
        # if pro_type_name == m4:
        #     t4 = threading.Thread(target=func4, args=(item.get("childNode", []), ))
        #     t4.start()
        #     threads.append(t4)
        # if pro_type_name == m5:
        #     t5 = threading.Thread(target=func5, args=(item.get("childNode", []), ))
        #     t5.start()
        #     threads.append(t5)
        # if pro_type_name == m6:
        #     t6 = threading.Thread(target=func6, args=(item.get("childNode", []), ))
        #     t6.start()
        #     threads.append(t6)
        # if pro_type_name == m7:
        #     t7 = threading.Thread(target=func7, args=(item.get("childNode", []), ))
        #     t7.start()
        #     threads.append(t7)
        # if pro_type_name == m8:
        #     t8 = threading.Thread(target=func8, args=(item.get("childNode", []), ))
        #     t8.start()
        #     threads.append(t8)
    for th in threads:
        th.join()
    print("over!")


if __name__ == "__main__":
    sidebar()








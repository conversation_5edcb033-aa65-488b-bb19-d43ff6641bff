# -*- coding: utf-8 -*-
"""
@File      : remote_analysis.py
<AUTHOR> caokun
@Email     : <EMAIL>
@License   : (C) Copyright 2025, Broadtech
@Created   : 2025/7/28 9:25
@IDE       : PyCharm
"""
import os
import time
import paramiko

from base.mysql_helper import MySqliteHelper
from main.config import config
from .logger import log, WARN, ERROR


class SSHConnection:
    def __init__(self):
        self.client = None

    def connect(self, ip, username, password):
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.client.connect(ip, 22, username, password)
            return True
        except Exception as err:
            log.log_screen_store(f"服务器连接失败 {ip}: {err}", level=ERROR)
            return False

    def disconnect(self):
        if self.client:
            self.client.close()
            self.client = None

    def exec_command(self, cmd):
        if not self.client:
            log.log_screen_store("服务器未连接", level=ERROR)
            return None, None, None

        try:
            stdin, stdout, stderr = self.client.exec_command(cmd)
            return stdin, stdout, stderr
        except Exception as e:
            log.log_screen_store(f"执行命令失败: {e}", level=ERROR)
            return None, None, None

    def is_connected(self):
        return self.client is not None


class RemoteAnalysiser:
    def __init__(self):
        self.file_connection = SSHConnection()
        self.analysis_connection = SSHConnection()
        self.outdated_pcap = []
        self.processed_file_path = os.path.join(config.BASE_PATH, "config", "processed_files.txt")
        self.load_processed_files()

    def load_processed_files(self):
        try:
            if os.path.exists(self.processed_file_path):
                with open(self.processed_file_path, 'r', encoding='utf-8') as f:
                    self.outdated_pcap = [line.strip() for line in f.readlines() if line.strip()]
            else:
                self.outdated_pcap = []
        except Exception as e:
            log.log_screen_store(f"加载已处理文件列表失败: {e}", level=ERROR)
            self.outdated_pcap = []

    def save_processed_files(self):
        try:
            with open(self.processed_file_path, 'w', encoding='utf-8') as f:
                for pcap_name in self.outdated_pcap:
                    f.write(f"{pcap_name}\n")
        except Exception as e:
            log.log_screen_store(f"保存已处理文件列表失败: {e}", level=ERROR)

    def add_processed_file(self, pcap_name):
        if pcap_name not in self.outdated_pcap:
            self.outdated_pcap.append(pcap_name)
            self.save_processed_files()

    def connect_file_server(self, ip, username, password):
        return self.file_connection.connect(ip, username, password)

    def connect_analysis_server(self, ip, username, password):
        return self.analysis_connection.connect(ip, username, password)

    def disconnect_all(self):
        self.file_connection.disconnect()

    def get_remote_pcap_files(self):
        pcap_files = {}

        try:
            if not self.file_connection.is_connected():
                if not self.connect_file_server(config.server_ip, config.server_user, config.server_password):
                    log.log_screen_store("无法连接文件服务器", level=ERROR)
                    return pcap_files

            cmd = f"ls -la {config.local_tmp_dir}"
            stdin, stdout, stderr = self.file_connection.exec_command(cmd)

            if stdout is None:
                return pcap_files

            files_output = stdout.read().decode('utf-8')
            stderr_output = stderr.read().decode('utf-8') if stderr else ""

            if stderr_output:
                return pcap_files

            lines = files_output.strip().split('\n')
            for line in lines:
                if line.startswith('-') or line.startswith('d'):
                    parts = line.split()
                    if len(parts) >= 9:
                        filename = parts[-1]

                        if (filename.endswith(('.pcap', '.pcapng')) and
                                not filename.endswith('.eth.pcap')):

                            if filename.endswith('.pcap'):
                                pcap_name = filename[:-5]
                            elif filename.endswith('.pcapng'):
                                pcap_name = filename[:-7]
                            else:
                                continue

                            pcap_path = str(os.path.join(config.local_tmp_dir, filename))
                            pcap_files[pcap_name] = pcap_path

            return pcap_files

        except Exception as err:
            log.log_screen_store(f"获取远程pcap文件失败: {err}", level=ERROR)
            return pcap_files

    def _convert_eth_remote(self, pcap: str) -> str:
        try:
            if not self.file_connection.is_connected():
                log.log_screen_store("服务器未连接，无法执行ETH转换", level=ERROR)
                return pcap

            convert_cmd = f"{config.eth_convert_path} {pcap} b2:36:67:5e:40:21 68:91:d0:65:3c:37"

            stdin, stdout, stderr = self.file_connection.exec_command(convert_cmd)

            if stdout is None:
                return pcap

            err = stderr.read().decode('utf-8') if stderr else ""

            if err:
                log.log_screen_store(f"{pcap} 远程转换ETH头异常: {err}", level=WARN)
                return pcap

            eth_pcap = str(pcap.replace(".pcap", ".eth.pcap"))
            return eth_pcap

        except Exception as e:
            log.log_screen_store(f"远程ETH转换失败: {e}", level=ERROR)
            return pcap

    def replay_pcap(self, pcap_path: str) -> bool:
        try:
            if not self.file_connection.is_connected():
                log.log_screen_store("服务器未连接，无法执行回放", level=ERROR)
                return False

            cmd = f"tcpreplay -i {config.net} --pps=300 {pcap_path}"
            log.log_screen_store(f"执行回放命令: {cmd}", level=WARN)

            stdin, stdout, stderr = self.file_connection.exec_command(cmd)

            out = stdout.read().decode('utf-8')

            if out:
                if "Successful" in out:
                    log.log_screen_store(f"{pcap_path} 回放成功", level=WARN)
                    return True
                else:
                    log.log_screen_store(f"{pcap_path} 回放失败，输出中未找到Successful", level=ERROR)
                    return False

        except Exception as e:
            log.log_screen_store(f"回放pcap文件失败: {e}", level=ERROR)
            return False

    def analysis_xdr(self, pcap_name: str):
        data = []
        try:
            if not self.connect_analysis_server(config.server_nan_ip, config.server_nan_user,
                                                config.server_nan_password):
                log.log_screen_store("无法连接分析服务器", level=ERROR)
                return data

            cmd = f"sh {config.ana_path} {pcap_name}"
            stdin, stdout, stderr = self.analysis_connection.exec_command(cmd)
            result = stdout.read().decode('utf-8')
            _, tmp, _ = result.split('++++++')
            tmp_s = tmp.split('\n')

            for idx, item in enumerate(tmp_s):
                if not item:
                    continue
                processed = list(map(lambda x: x.strip(), item.split('\t')))
                data.append(processed)

        except Exception as err:
            log.log_screen_store(f"XDR分析异常: {err}", level=ERROR)
        finally:
            self.analysis_connection.disconnect()
        return data

    @staticmethod
    def save_xdr_to_db(sql_helper: MySqliteHelper, data):
        try:
            for row in data:
                xdr_type = row[0]
                big_class = row[1]
                value = row[2]
                upstream_traffic = row[3]
                downstream_traffic = row[4]
                upstream_packets = row[5]
                downstream_packets = row[6]
                pcap_name = row[7]
                proportion = row[8]
                total = row[9]

                result = sql_helper.query(f"select id, name from stream_recognition where pcap_name='{pcap_name}'")

                if not result:
                    log.log_screen_store(f"未找到pcap_name={pcap_name}的记录，跳过", level=WARN)
                    continue

                stream_recognition_id = result[0][0]
                name = result[0][1]

                insert_sql = """
                INSERT INTO xdr_data (xdr_type, big_class, value, name, upstream_traffic, 
                                    downstream_traffic, upstream_packets, downstream_packets, 
                                    pcap_name, proportion, total, stream_recognition_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                params = [xdr_type, big_class, value, name, upstream_traffic,
                          downstream_traffic, upstream_packets, downstream_packets,
                          pcap_name, proportion, total, stream_recognition_id]

                sql_helper.update(insert_sql, params)

        except Exception as e:
            log.log_screen_store(f"保存XDR数据到数据库失败: {e}", level=ERROR)

    def remote_analysis(self):
        try:
            while True:
                pcap_dict = self.get_remote_pcap_files()
                if not pcap_dict:
                    log.log_screen_store("未找到新的pcap文件，等待30秒后重试", level=WARN)
                    time.sleep(30)
                for pcap_name, pcap_path in pcap_dict.items():
                    if pcap_name in self.outdated_pcap:
                        log.log_screen_store(f"文件 {pcap_name} 已处理，跳过", level=WARN)
                        continue
                    eth_pcap_path = self._convert_eth_remote(pcap_path)
                    replay_success = self.replay_pcap(eth_pcap_path)
                    if replay_success:
                        self.add_processed_file(pcap_name)
                        time.sleep(300)
                        analysis_result = self.analysis_xdr(pcap_name)
                        if analysis_result:
                            try:
                                sql_helper = MySqliteHelper()
                                if not sql_helper.connect(config.script_db):
                                    return
                                self.save_xdr_to_db(sql_helper, analysis_result)
                            except Exception as e:
                                log.log_screen_store(f"保存XDR数据失败: {e}", level=ERROR)
                time.sleep(30)

        except Exception as err:
            log.log_screen_store(f"远程分析过程中出现异常: {err}", level=ERROR)
        finally:
            self.disconnect_all()
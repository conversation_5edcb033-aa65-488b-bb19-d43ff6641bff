#!/usr/bin/env python
# -*- coding: utf-8 -*-

from appium import webdriver
import yaml
import os
from base.logger import log


def load_config():
    conf_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'apps.yaml')
    # logger.info('load config with %s...' % conf_file)
    with open(conf_file, 'r') as f:
        conf = yaml.load(f, Loader=yaml.FullLoader)
    if conf:
        return conf


def desired_cap(app_package=None, app_activity=None):
    conf = load_config()
    desired_caps = dict()
    desired_caps['platformName'] = conf['desiredCaps']['platformName']
    desired_caps['platformVersion'] = str(conf['desiredCaps']['platformVersion'])
    desired_caps['deviceName'] = conf['desiredCaps']['deviceName']
    desired_caps['appPackage'] = app_package
    desired_caps['appActivity'] = app_activity
    desired_caps['noReset'] = conf['desiredCaps']['noReset']
    desired_caps['automationNmae'] = conf['desiredCaps']['automationNmae']
    desired_caps['newCommandTimeout'] = conf['desiredCaps']['newCommandTimeout']
    desired_caps['unicodeKeyboard'] = conf['desiredCaps']['unicodeKeyboard']
    desired_caps['resetKeyboard'] = conf['desiredCaps']['resetKeyboard']
    log.log_screen_store('start [%s] app...' % app_package)
    driver = webdriver.Remote('http://{ip}:{port}/wd/hub'.format(
        ip=conf['desiredCaps']['ip'], port=str(conf['desiredCaps']['port'])), desired_caps)
    driver.implicitly_wait(10)

    return driver


def quite_driver(driver):
    driver.quit()


if __name__ == '__main__':
    desired_cap('com.tencent.mm', '.ui.LauncherUI')


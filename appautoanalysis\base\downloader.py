# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON><PERSON><PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   downloader.py
@Time    :   2023/7/6 13:38
"""
import os
import time
import sys
import datetime
from datetime import timedelta
import socket
import importlib
import json
from pathlib import Path

import requests
from airtest.core.api import Template, exists
from airtest.core.helper import G
from multiprocessing import Lock
from .capture import CaptureTool
from .logger import log, WARN, INFO
from main.config import config
from .common import update_script_db, ScpClient, get_socket_client, search_air, write_excel
from .devices_driver import Driver
from .remote_analysis import RemoteAnalysiser


def set_current_driver(device_info):
    """设置当前driver"""
    while True:
        try:
            log.log_screen_store(f"connect device...")
            driver = Driver(device_info)
            flag = driver.connect()
            if not flag:
                time.sleep(5)
                continue
            log.log_screen_store("connect device successful")
            time.sleep(5)
            return driver
        except Exception as e:
            log.log_screen_store(f"{device_info.get('uuid')}-链接driver异常-{str(e)}", level=WARN)
        time.sleep(10)


class Downloader(object):
    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        self.driver = driver
        self.device_info = device_info
        self.device_nu = device_info.get("uuid")
        self.lock = lock
        self.write_lock = write_lock
        self.file_path = None
        self.ct = CaptureTool(device_info=device_info)
        self.app_info = self.AppInfo(device_info)

    class AppInfo(object):
        def __init__(self, device: dict):
            self.package = ""
            self.success = True
            self.remark = ""
            self.found = False
            self.name = ""
            self.id = ""
            self.is_blocked = "N"
            self.l_class = ""
            self.result_sheet = ""
            self.app_url = ""
            self.app_id = ""
            self.before = ""
            self.pcap_before = ""
            self.after = ""
            self.pcap_after = ""
            self.pcap = ""
            self.mobile_ip = device.get("mobile_ip")
            self.router_host = device.get("router_host")
            self.router_port = device.get("router_port")
            self.extend_device = device.get("extend_device")
            self.start_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')
            self.end_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')

        def set_info(self, info: dict):
            self.package = info.get(config.PACKAGE, "")
            self.name = info.get(config.NAME, "")
            self.id = info.get(config.ID, "")
            self.l_class = info.get(config.L_CLASS, "")
            self.result_sheet = info.get(config.RESULT_SHEET, "")
            self.app_url = info.get(config.APP_URL, "")
            self.app_id = info.get(config.APP_ID, "")
            self.remark = info.get(config.REMARK, "")
            self.is_blocked = info.get(config.IS_BLOCKED, "N")
            self.success = info.get(config.SUCCESS, True)
            self.before = info.get(config.BEFORE, "")
            self.after = info.get(config.AFTER, "")
            self.pcap = info.get(config.PCAP, "")
            self.pcap_before = info.get(config.PCAP_BEFORE, "")
            self.pcap_after = info.get(config.PCAP_AFTER, "")
            self.found = info.get(config.FOUND, False)
            self.start_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')
            self.end_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')

        def get_info(self):
            return {
                config.ID: self.id,
                config.PACKAGE: self.package,
                config.NAME: self.name,
                config.REMARK: self.remark,
                config.IS_BLOCKED: self.is_blocked,
                config.L_CLASS: self.l_class,
                config.RESULT_SHEET: self.result_sheet,
                config.APP_ID: self.app_id,
                config.PCAP: self.pcap,
                config.BEFORE: self.before,
                config.AFTER: self.after,
                config.PCAP_BEFORE: self.pcap_before,
                config.PCAP_AFTER: self.pcap_after,
                config.MOBILE_IP: self.mobile_ip,
                config.ROUTER_HOST: self.router_host,
                config.ROUTER_PORT: self.router_port,
                config.EXTEND_DEVICE: self.extend_device,
                config.START_TIME: self.start_time,
                config.END_TIME: self.end_time
            }

    def download(self, app_info: dict) -> dict:
        """下载"""
        pass

    def app_store(self) -> None:
        """应用商店"""
        pass

    def search_app(self) -> None:
        """查找应用"""
        pass

    def install_app(self) -> None:
        """安装应用"""
        pass

    def download_status(self) -> None:
        """安装状态"""
        pass

    def open_app_store(self) -> None:
        """应用商店抓包"""
        pass

    def open_browser(self, app_info: dict) -> dict:
        """浏览器抓包"""
        pass


    def capture_pcap(self):
        package_name = self.app_info.package
        user_id_msg = self.ct.get_userid_by_packagename(package_name)
        if not user_id_msg["success"]:
            self.app_info.remark = user_id_msg["remark"]
            self.app_info.success = False
            return
        if config.platform_type == 1:
            pcap_name = f"cn_android_{self.app_info.l_class}_{int(time.time())}.pcap"
        else:
            pcap_name = f"en_android_{self.app_info.l_class}_{int(time.time())}.pcap"
        user_id = user_id_msg["user_id"]
        self.ct.clear_environment(user_id)
        if not self.ct.add_iptables(user_id, package_name):
            self.app_info.success = False
            self.app_info.remark = "添加iptables异常"
            return
        progress = self.ct.start_tcpdump(user_id, pcap_name)
        if progress is None or isinstance(progress, tuple):
            self.app_info.success = False
            self.app_info.remark = "tcpdump启动异常"
            self.ct.clear_environment(user_id)
            return
        self.block_analysis()
        self.ct.clear_environment(user_id)
        success = self.ct.pull_pcap_to_local(user_id, package_name, pcap_name)
        if not success:
            self.app_info.success = False
            self.app_info.remark = "拉取pcap包到本地异常"
            return
        self.ct.remove_pcap(pcap_name)
        self.app_info.pcap = os.path.join(config.local_tmp_dir, pcap_name)
        if not os.path.exists(self.app_info.pcap) or os.path.getsize(self.app_info.pcap) == 0:
            self.app_info.remark = "pcap包文件不存在或大小异常"
            self.app_info.success = False
            return
        flag, remark_pcap = self.upload_pcap(self.app_info.pcap)
        if not flag:
            self.app_info.remark = remark_pcap
            self.app_info.success = False


    def start_app_capture(self, screenshot_file_dir: str) -> None:
        """开始抓包"""
        appid = self.app_info.app_id
        app_id = str(appid) if isinstance(appid, int) else appid
        package_name = self.app_info.package
        user_id_msg = self.ct.get_userid_by_packagename(package_name)
        if not user_id_msg["success"]:
            self.app_info.remark = user_id_msg["remark"]
            self.app_info.success = False
            return
        user_id = user_id_msg["user_id"]
        capture_info = self.ct.try_capture(user_id, package_name, app_id, screenshot_file_dir)
        if not capture_info["success"]:
            self.app_info.remark = capture_info["remark"]
            self.app_info.success = False
            return
        if 'before' in screenshot_file_dir:
            self.app_info.before = screenshot_file_dir
            self.app_info.pcap_before = capture_info["pcap"]
        elif 'after' in screenshot_file_dir:
            self.app_info.after = screenshot_file_dir
            self.app_info.pcap_after = capture_info["pcap"]
        self.app_info.pcap = capture_info["pcap"]
        # self.set_current_driver()
        # self.driver.terminate(package_name)
        # self.quit_driver()

    def start_browser_capture(self, app_info: dict) -> dict:
        pass

    @staticmethod
    def upload_pcap(pcap: str) -> tuple:
        """上传pcap"""
        scp_client = ScpClient()
        pcap_name = os.path.basename(pcap)
        remote_path = config.server_pcap_path + pcap_name
        flag = scp_client.connect(config.server_ip, config.server_user, config.server_password)
        if not flag:
            log.log_screen_store("上传pcap包异常", level=WARN)
            return flag, "上传pcap包异常"
        flag = scp_client.transmission(pcap, remote_path)
        scp_client.close()
        if not flag:
            log.log_screen_store("上传pcap包异常", level=WARN)
            return flag, "上传pcap包异常"
        return True, remote_path

    @staticmethod
    def download_csv():
        local_dir = Path(config.BASE_PATH) / 'tmp' / 'csv_downloads'
        local_dir.mkdir(parents=True, exist_ok=True)
        scp_client = ScpClient()
        try:
            flag = scp_client.connect(config.server_ip, config.server_user, config.server_password)
            if not flag:
                return False, '连接服务器失败'
            files = scp_client.scp.listdir(config.remote_dir)
            if 'result.csv' not in files:
                scp_client.close()
                return False, '该目录下没有result.csv文件'
            local_path = local_dir / "result.csv"
            remote_file_path = config.remote_dir.rstrip('/') + '/result.csv'
            flag = scp_client.download(remote_file_path, str(local_path))
            scp_client.close()
            if not flag:
                return False, '下载失败'
            return True, str(local_path)
        except Exception as e:
            scp_client.close()
            return False, f'下载异常: {e}'


    def correlating(self):
        """获取关联ID/阻断分析结果"""
        client = get_socket_client()
        try:
            msg = json.dumps(self.app_info.get_info())
            client.sendall(msg.encode("utf-8"))
            recv_msg = client.recv(1024 * 4)
            correlation = json.loads(recv_msg.decode("utf-8"))
            self.app_info.set_info(correlation)
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-解析pcap包异常", level=WARN)
            self.app_info.remark = "解析pcap包异常"
            self.app_info.success = False
        finally:
            client.close()

    def correlation(self):
        """关联ID分析"""
        pcap = self.app_info.pcap
        if not os.path.exists(pcap) or os.path.getsize(pcap) == 0:
            self.app_info.remark = "pcap包文件不存在或大小异常"
            self.app_info.success = False
            return
        flag, remark_pcap = self.upload_pcap(pcap)
        if not flag:
            self.app_info.remark = remark_pcap
            self.app_info.success = False
        else:
            self.app_info.pcap = remark_pcap
            self.correlating()

    def block_analysis(self):
        """验证阻断"""
        l_class = self.app_info.l_class

        lang_prefix = "cn" if config.platform_type in [1, 3, 5] else "en"
        platform = self.device_info["platform"].lower()

        if config.mode == 0:
            suffix = "case"
        else:
            suffix = "block"

        pre_search = f"brd_{lang_prefix}_{platform}_{suffix}_{l_class}"
        script_air = search_air(pre_search)
        if script_air:
            if script_air not in sys.path:
                sys.path.append(script_air)
        else:
            self.app_info.remark = "未找到录制脚本"
            self.app_info.is_blocked = "N"
            return
        self.ct.clear_app_cache(self.app_info.package)
        try:
            _, filename = os.path.split(script_air)
            script_module = filename[:-4]
            module = importlib.import_module(script_module)
            importlib.reload(module)
            start_playback = getattr(module, "reappear_blocked")
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-脚本格式异常！-{script_air}-{str(e)}")
            self.app_info.remark = "脚本格式异常"
            self.app_info.is_blocked = "N"
            return
        # common_air = os.path.join(config.BASE_PATH, "plugins", "common.air")
        # copy_remove(common_air, script_air)
        self.device_info["router_index"] = l_class
        logdir = os.path.join(config.SCREEN_SHOT_DIR, str(l_class))
        G.BASEDIR.append(script_air)
        self.driver.scenes.clear()
        self.driver.logdir = logdir
        self.driver.create_logdir(logdir)
        self.driver.clear_dir()
        result = None
        try:
            current_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')
            self.driver.start_time, self.driver.end_time = current_time, current_time
            result = start_playback(dd=self.driver, l_class=l_class, dev_router=self.device_info)
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-脚本运行异常！-{script_air}-{str(e)}")
        if isinstance(result, str):
            self.app_info.is_blocked = "N"
            self.app_info.remark = result
        elif result:
            self.app_info.is_blocked = "Y"
        else:
            self.app_info.is_blocked = "N"
        self.app_info.start_time, self.app_info.end_time = self.driver.start_time, self.driver.end_time
        self.driver.logdir = ""
        G.BASEDIR.clear()
        # copy_remove(destination=script_air, copy=False)

    # def rule_handle(self, app_info: dict, clean: int = 0) -> dict:
    #     # 进行规则下发
    #     app_id = app_info.get(config.L_CLASS, "")
    #     router = Router(self.device_info)
    #     con_result = router.connect()
    #     if not con_result:
    #         app_info['success'] = False
    #         if clean:
    #             app_info[config.REMARK] = "阻断规则清除失败，无法连接下发服务器！"
    #         else:
    #             app_info[config.REMARK] = "阻断规则下发失败，无法连接下发服务器！"
    #         return app_info
    #     result = router.exe_invoke_shell(app_id, clean)
    #     if not result:
    #         app_info['success'] = False
    #         if clean:
    #             app_info[config.REMARK] = "因未知原因阻断规则清除失败!"
    #         else:
    #             app_info[config.REMARK] = "因未知原因阻断规则下发失败!"
    #     router.close()
    #     return app_info


class XiaoMi(Downloader):

    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        super().__init__(lock, write_lock, device_info, driver)

    def network_error(self) -> bool:
        err_note = ["努力加载中", "加载失败", "开始网络诊断"]
        for item in err_note:
            match = ".*" + item + ".*"
            if self.driver.poco(textMatches=match).wait(timeout=2).exists():
                return True
        return False

    def search_app(self) -> None:
        """查找应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        while True:
            try:
                time.sleep(10)
                search_flag = False
                self.driver.poco(name="com.xiaomi.market:id/search_text_switcher").wait(timeout=20).click()
                self.driver.poco(name="android:id/input").wait(timeout=20).set_text(app_name)
                self.driver.driver.keyevent("KEYCODE_ENTER")
                time.sleep(10)
                if self.network_error():
                    raise ValueError("网络异常")
                matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
                for match in matches:
                    search_flag = self.driver.poco(name="com.xiaomi.market:id/display_name",
                                                   textMatches=match).wait(timeout=5).exists()
                    if search_flag:
                        break
                break
            except Exception as e:
                log.log_screen_store(f"{self.device_nu}-搜索应用{_app_name}异常！", level=WARN)
                package = self.device_info.get("xiaomi")
                self.interference()
                self.driver.stop_app(package)
                time.sleep(2)
                self.driver.start_app(package)
        if not search_flag:
            self.app_info.success = False
            self.app_info.remark = "小米应用商店未找到"
            log.log_screen_store(f"{self.device_nu}-应用商店未找到-{_app_name}", level=WARN)
            self.driver.return_home()
            return
        self.app_info.found = True

    def install_app_online(self) -> None:
        """安装应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
        install_exists = False
        try:
            for match in matches:
                if self.driver.poco(name="com.xiaomi.market:id/display_name", textMatches=match).wait(timeout=5).exists():
                    self.driver.poco(name="com.xiaomi.market:id/display_name", textMatches=match).click()
                    break
            install_exists = self.driver.poco(name="com.xiaomi.market:id/detail_action_container").offspring("安装").exists()
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-安装异常-{_app_name}", level=WARN)
        if not install_exists:
            self.app_info.success = False
            self.app_info.remark = "应用程序已安装或无法下载"
            return
        try:
            self.driver.poco(name="com.xiaomi.market:id/detail_action_container").offspring("安装").click()
        except Exception as e:
            self.app_info.success = False
            self.app_info.remark = "应用程序安装异常"

    def install_app(self) -> None:
        """安装应用"""
        log.log_screen_store('找到设备[%s]，将开始进行app安装操作...' % self.device_nu, level=INFO)
        try_time = 1
        package_name = self.app_info.package
        app_name = self.app_info.name
        before_third_apps = []
        if not package_name:
            before_third_apps = self.ct.get_third_app_list()
        while try_time < 4:
            file_obj = os.popen(f'{config.adb} -s {self.device_nu} install {self.file_path}')
            install_msg = file_obj.read()
            if 'Success' in install_msg:
                if not package_name:
                    after_third_apps = self.ct.get_third_app_list()
                    packages = list(set(after_third_apps).difference(set(before_third_apps)))
                    if len(packages) == 1:
                        _, self.app_info.package = packages[0].split(":")
                    else:
                        self.app_info.success = False
                        self.app_info.remark = "无法确定APK的包名"
                        log.log_screen_store(f"{self.device_nu}-无法确定APK的package name", level=WARN)
                file_obj.close()
                log.log_screen_store(f'{self.device_nu}-{app_name}安装成功', level=INFO)
                return
            log.log_screen_store(f'{self.device_nu}-{app_name}安装失败，尝试第{try_time + 1}/{try_time}次安装...', level=WARN)
            time.sleep(10)
            file_obj.close()
            try_time += 1
        self.app_info.success = False
        self.app_info.remark = "应用安装失败"

    def app_store(self) -> None:
        """应用商店"""
        before_third_apps = self.ct.get_third_app_list()
        self.search_app()
        if not self.app_info.success:
            return
        self.install_app_online()
        if not self.app_info.success:
            return
        self.download_status()
        if not self.app_info.success:
            return
        after_third_apps = self.ct.get_third_app_list()
        packages = list(set(after_third_apps).difference(set(before_third_apps)))
        if len(packages) == 1:
            _, package_name = packages[0].split(":")
            self.app_info.package = package_name
        else:
            self.app_info.success = False
            self.app_info.remark = "无法确定APK的包名"

    def download_status(self) -> None:
        """下载状态"""
        app_name = self.app_info.name
        start_time = time.time()
        try:
            search = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1710313495717.png")
            while True:
                # flag = self.driver.poco(name="打开").exists()
                flag = exists(Template(search, threshold=0.9))
                if flag:
                    log.log_screen_store(
                        f"{self.device_nu}-安装应用{app_name}成功，安装耗时{int(time.time() - start_time)}秒")
                    break
                if time.time() - start_time > config.app_install_timeout:
                    log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                    self.app_info.success = False
                    self.app_info.remark = "下载超时"
                    break
                log.log_screen_store(f"{self.device_nu}-正在下载应用{app_name}")
                time.sleep(20)
            self.driver.return_home()
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-下载应用{app_name}异常", level=WARN)
            self.app_info.success = False
            self.app_info.remark = "下载异常"

    def open_app_store(self) -> None:
        """应用商店抓包"""
        self.interference()
        package = self.device_info.get("xiaomi")
        self.driver.start_app(package)
        time.sleep(5)
        self.interference()
        self.app_store()
        self.driver.stop_app(package)

    def interference(self):
        """点掉android系统可能的弹窗"""
        path = os.path.join(config.BASE_PATH, "resource", "screenshot", "interference", "android")
        pics = os.listdir(path)
        for item in pics:
            tmp = Template(os.path.join(path, item))
            if exists(tmp):
                self.driver.click(tmp)

    # def open_browser(self, app_info: dict) -> dict:
    #     """使用浏览器抓包"""
    #     return self.start_browser_capture(app_info)

    def download(self, app_info: dict):
        self.app_info.set_info(app_info)
        self.app_info.found = self.ct.exists(self.app_info.package)
        if not self.app_info.found:
            if self.app_info.app_url:
                self.download_apk_by_url()
                if not self.app_info.success:
                    self.open_app_store()
            else:
                self.open_app_store()
        if self.app_info.success:
            self.lock.acquire()
            if config.mode == 1:
                self.capture_pcap()
            elif config.mode == 0:
                self.block_analysis()
            self.lock.release()
        # write_excel(self.app_info.get_info(), self.app_info.result_sheet, self.write_lock)
        try:
            if self.app_info.package:
                self.ct.uninstall(self.app_info.package)
        except Exception as e:
            log.log_screen_store(f"{self.ct.devices_no}-卸载应用异常！package_name={self.app_info.package}", level=WARN)

    def download_apk_by_url(self) -> None:
        file_name = str(self.app_info.l_class) + '.apk'
        url = self.app_info.app_url
        self.file_path = os.path.join(config.URL_DOWNLODER_DIR, file_name)
        if not os.path.exists(self.file_path):
            try:
                # 发送请求并下载文件, stream=True 设置为流读取
                response = requests.get(url, stream=True, timeout=(21, 60))
                with open(self.file_path, 'wb') as file:
                    for chunk in response.iter_content(chunk_size=2048):
                        if chunk:
                            file.write(chunk)
                log.log_screen_store(f"{url}下载成功!", level=INFO)
            except Exception as e:
                log.log_screen_store(f"{url}下载失败!", level=WARN)
                self.file_path = None
                self.app_info.success = False
                self.app_info.remark = "应用下载失败"
                return
        self.install_app()
        if os.path.exists(self.file_path):
            os.remove(self.file_path)
        return


class YingYongBao(Downloader):

    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        super().__init__(lock, write_lock, device_info, driver)

    def network_error(self) -> bool:
        err_note = ["网络已断开", "网络设置", "重新加载", "正在加载"]
        for item in err_note:
            match = ".*" + item + ".*"
            if self.driver.poco(textMatches=match).wait(timeout=2).exists():
                return True
        return False

    def search_app(self) -> None:
        """查找应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        while True:
            try:
                time.sleep(10)
                search_flag = False
                self.driver.poco_click(name="com.tencent.android.qqdownloader:id/awy")
                self.driver.poco_input(app_name, name="com.tencent.android.qqdownloader:id/yv")
                self.driver.driver.keyevent("KEYCODE_ENTER")
                time.sleep(10)
                if self.network_error():
                    raise ValueError("网络异常")
                matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
                for match in matches:
                    search_flag = self.driver.poco(name="android.widget.TextView",
                                                   textMatches=match).wait(timeout=5).exists()
                    if search_flag:
                        break
                break
            except Exception as e:
                log.log_screen_store(f"{self.device_nu}-搜索应用{_app_name}异常！", level=WARN)
                package = self.device_info.get("xiaomi")
                self.interference()
                self.driver.stop_app(package)
                time.sleep(2)
                self.driver.start_app(package)
        if not search_flag:
            self.app_info.success = False
            self.app_info.remark = "应用宝商店未找到"
            log.log_screen_store(f"{self.device_nu}-应用商店未找到-{_app_name}", level=WARN)
            self.driver.return_home()
            return
        self.app_info.found = True

    def install_app_online(self) -> None:
        """安装应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
        install_exists = False
        uninstall = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723450700022.png")
        try:
            for match in matches:
                if self.driver.poco(name="android.widget.TextView", textMatches=match).wait(timeout=5).exists():
                    self.driver.poco(name="android.widget.TextView", textMatches=match).click()
                    break
            install_exists = exists(Template(uninstall, threshold=0.9))
            if install_exists:
                self.driver.click(Template(uninstall, threshold=0.9))
            else:
                install_exists = self.driver.poco(name="com.tencent.android.qqdownloader:id/wt",
                                                  textMatches="立即下载.*").wait(timeout=5).exists()
                if install_exists:
                    self.driver.poco(name="com.tencent.android.qqdownloader:id/wt", textMatches="立即下载.*").click()
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-安装异常-{_app_name}", level=WARN)
        if not install_exists:
            self.app_info.success = False
            self.app_info.remark = "应用程序已安装或无法下载"
            return
        tmp1 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723451580766.png")
        tmp2 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723451667571.png")
        if exists(Template(tmp1)):
            self.driver.click(Template(tmp2))

    def install_app(self) -> None:
        """安装应用"""
        log.log_screen_store('找到设备[%s]，将开始进行app安装操作...' % self.device_nu, level=INFO)
        try_time = 1
        package_name = self.app_info.package
        app_name = self.app_info.name
        before_third_apps = []
        if not package_name:
            before_third_apps = self.ct.get_third_app_list()
        while try_time < 4:
            file_obj = os.popen(f'{config.adb} -s {self.device_nu} install {self.file_path}')
            install_msg = file_obj.read()
            if 'Success' in install_msg:
                if not package_name:
                    after_third_apps = self.ct.get_third_app_list()
                    packages = list(set(after_third_apps).difference(set(before_third_apps)))
                    if len(packages) == 1:
                        _, self.app_info.package = packages[0].split(":")
                    else:
                        self.app_info.success = False
                        self.app_info.remark = "无法确定APK的包名"
                        log.log_screen_store(f"{self.device_nu}-无法确定APK的package name", level=WARN)
                file_obj.close()
                log.log_screen_store(f'{self.device_nu}-{app_name}安装成功', level=INFO)
                return
            log.log_screen_store(f'{self.device_nu}-{app_name}安装失败，尝试第{try_time + 1}/{try_time}次安装...', level=WARN)
            time.sleep(10)
            file_obj.close()
            try_time += 1
        self.app_info.success = False
        self.app_info.remark = "应用安装失败"

    def app_store(self) -> None:
        """应用商店"""
        before_third_apps = self.ct.get_third_app_list()
        self.search_app()
        if not self.app_info.success:
            return
        self.install_app_online()
        if not self.app_info.success:
            return
        self.download_status()
        if not self.app_info.success:
            return
        after_third_apps = self.ct.get_third_app_list()
        packages = list(set(after_third_apps).difference(set(before_third_apps)))
        if len(packages) == 1:
            _, package_name = packages[0].split(":")
            self.app_info.package = package_name
        else:
            self.app_info.success = False
            self.app_info.remark = "无法确定APK的包名"

    def download_status(self) -> None:
        """下载状态"""
        app_name = self.app_info.name
        start_time = time.time()
        try:
            search1 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723453891168.png")
            while True:
                flag1 = exists(Template(search1))
                if flag1:
                    install_button = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723687131945.png")
                    self.driver.click(Template(install_button, record_pos=(0.325, 0.153), resolution=(1220, 2712)))
                    search2 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723454491089.png")
                    search3 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1723456100477.png")
                    while True:
                        flag2 = exists(Template(search2)) or exists(Template(search3))
                        if flag2:
                            log.log_screen_store(
                                f"{self.device_nu}-安装应用{app_name}成功，安装耗时{int(time.time() - start_time)}秒")
                            return
                        if time.time() - start_time > config.app_install_timeout:
                            self.app_info.success = False
                            self.app_info.remark = "下载超时"
                            log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                            return
                        time.sleep(20)
                if time.time() - start_time > config.app_install_timeout:
                    self.app_info.success = False
                    self.app_info.remark = "下载超时"
                    log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                    break
                time.sleep(20)
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-下载应用{app_name}异常", level=WARN)
            self.app_info.success = False
            self.app_info.remark = "下载异常"
        finally:
            self.driver.return_home()

    def open_app_store(self) -> None:
        """应用商店抓包"""
        self.interference()
        package = self.device_info.get("yingyongbao")
        self.driver.start_app(package)
        time.sleep(5)
        self.interference()
        self.app_store()
        self.driver.stop_app(package)

    def interference(self):
        """点掉android系统可能的弹窗"""
        path = os.path.join(config.BASE_PATH, "resource", "screenshot", "interference", "android")
        pics = os.listdir(path)
        for item in pics:
            tmp = Template(os.path.join(path, item))
            if exists(tmp):
                self.driver.click(tmp)

    # def open_browser(self, app_info: dict) -> dict:
    #     """使用浏览器抓包"""
    #     return self.start_browser_capture(app_info)

    def download(self, app_info: dict):
        self.app_info.set_info(app_info)
        self.app_info.found = self.ct.exists(self.app_info.package)
        if not self.app_info.found:
            if self.app_info.app_url:
                self.download_apk_by_url()
                if not self.app_info.success:
                    self.open_app_store()
            else:
                self.open_app_store()
        if self.app_info.success:
            if config.mode == 1:
                self.capture_pcap()
            elif config.mode == 0:
                self.block_analysis()
            self.lock.acquire()
            self.lock.release()
            # write_excel(self.app_info.get_info(), self.app_info.result_sheet, self.write_lock)
        try:
            if self.app_info.package:
                self.ct.uninstall(self.app_info.package)
        except Exception as e:
            log.log_screen_store(f"{self.ct.devices_no}-卸载应用异常！package_name={self.app_info.package}", level=WARN)

    def download_apk_by_url(self) -> None:
        file_name = str(self.app_info.l_class) + '.apk'
        url = self.app_info.app_url
        self.file_path = os.path.join(config.URL_DOWNLODER_DIR, file_name)
        if not os.path.exists(self.file_path):
            try:
                # 发送请求并下载文件, stream=True 设置为流读取
                response = requests.get(url, stream=True, timeout=(21, 60))
                with open(self.file_path, 'wb') as file:
                    for chunk in response.iter_content(chunk_size=2048):
                        if chunk:
                            file.write(chunk)
                log.log_screen_store(f"{url}下载成功!", level=INFO)
            except Exception as e:
                log.log_screen_store(f"{url}下载失败!", level=WARN)
                self.file_path = None
                self.app_info.success = False
                self.app_info.remark = "应用下载失败"
                return
        self.install_app()
        if os.path.exists(self.file_path):
            os.remove(self.file_path)
        return


class Google(Downloader):

    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        super().__init__(lock, write_lock, device_info, driver)

    def network_error(self) -> bool:
        err_note = ["超过", "评分", "安装", "广告", "找不到"]
        for item in err_note:
            match = "[\s\S]*" + item + "[\s\S]*"
            if (self.driver.poco(textMatches=match).wait(timeout=2).exists() or
                    self.driver.poco(nameMatches=match).wait(timeout=2).exists() or
                    self.driver.poco(descMatches=match).wait(timeout=2).exists()):
                return True
        return False

    def search_app(self):
        """查找应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        while True:
            try:
                time.sleep(10)
                found = False
                self.driver.poco_click(name="在 Google Play 中搜索")
                self.driver.poco_quic(_app_name, name="android.widget.EditText")
                self.driver.driver.keyevent("KEYCODE_ENTER")
                time.sleep(10)
                if not self.network_error():
                    raise ValueError("网络异常")
                matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?[\s\S]*$"]
                for match in matches:
                    found = self.driver.poco(nameMatches=match).wait(timeout=5).exists()
                    if found:
                        break
                break
            except Exception as e:
                log.log_screen_store(f"{self.device_nu}-搜索应用{_app_name}异常", level=WARN)
                package = self.device_info.get("google_play")
                self.interference()
                self.driver.stop_app(package)
                time.sleep(2)
                self.driver.start_app(package)
        if not found:
            self.app_info.success = False
            self.app_info.remark = "Google Play应用商店未找到"
            self.driver.return_home()
            return
        self.app_info.found = True

    def install_app_online(self) -> None:
        """安装应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        install_exists = False
        matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?[\s\S]*$"]
        found = False
        try:
            for match in matches:
                found = self.driver.poco(nameMatches=match).wait(timeout=5).exists()
                if found:
                    self.driver.poco(nameMatches=match).click()
                    install_exists = self.driver.poco(type="android.widget.TextView", text="安装").exists()
                    break
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-应用程序{app_name}安装异常", level=WARN)
        if not found or not install_exists:
            self.app_info.success = False
            self.app_info.remark = "应用程序已安装或无法下载"
            log.log_screen_store(f"{self.device_nu}-应用程序{app_name}已安装，请先卸载", level=WARN)
            return
        try:
            self.driver.poco(type="android.widget.TextView", text="安装").click()
        except Exception as e:
            self.app_info.success = False
            self.app_info.remark = "应用程序安装异常"

    def app_store(self):
        """应用商店"""
        before_third_apps = self.ct.get_third_app_list()
        self.search_app()
        if not self.app_info.success:
            return
        self.install_app_online()
        if not self.app_info.success:
            return
        self.download_status()
        if not self.app_info.success:
            return
        after_third_apps = self.ct.get_third_app_list()
        packages = list(set(after_third_apps).difference(set(before_third_apps)))
        if len(packages) == 1:
            _, self.app_info.package = packages[0].split(":")
        else:
            self.app_info.success = False
            self.app_info.remark = "无法确定APK的包名"

    def download_status(self):
        """下载状态"""
        app_name = self.app_info.name
        start_time = time.time()
        try:
            search = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1710402931437.png")
            while True:
                flag = exists(Template(search, threshold=0.9))
                if flag:
                    log.log_screen_store(
                        f"{self.device_nu}-安装应用{app_name}成功，安装耗时{int(time.time() - start_time)}秒")
                    break
                if time.time() - start_time > config.app_install_timeout:
                    log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                    self.app_info.success = False
                    self.app_info.remark = "下载超时"
                    break
                log.log_screen_store(f"{self.device_nu}-正在下载应用{app_name}")
                time.sleep(20)
            self.driver.return_home()
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-下载应用{app_name}异常！", level=WARN)
            self.app_info.success = False
            self.app_info.remark = "下载异常"

    def open_app_store(self) -> None:
        """应用商店抓包"""
        self.interference()
        package = self.device_info.get("google_play")
        self.driver.start_app(package)
        time.sleep(5)
        self.interference()
        self.app_store()
        self.driver.stop_app(package)

    def interference(self):
        """点掉android系统可能的弹窗"""
        path = os.path.join(config.BASE_PATH, "resource", "screenshot", "interference", "android")
        pics = os.listdir(path)
        for item in pics:
            tmp = Template(os.path.join(path, item))
            if exists(tmp):
                self.driver.click(tmp)

    def download(self, app_info: dict):
        self.app_info.set_info(app_info)
        self.app_info.found = self.ct.exists(self.app_info.package)
        if not self.app_info.found:
            self.open_app_store()
        if self.app_info.success:
            if config.mode == 1:
                self.capture_pcap()
            elif config.mode == 0:
                self.block_analysis()
            self.lock.acquire()
            self.lock.release()
            # write_excel(self.app_info.get_info(), self.app_info.result_sheet, self.write_lock)
        try:
            if self.app_info.package:
                self.ct.uninstall(self.app_info.package)
        except Exception as e:
            log.log_screen_store(f"{self.ct.devices_no}-卸载应用异常！package_name={self.app_info.package}", level=WARN)


class ApkPure(Downloader):

    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        super().__init__(lock, write_lock, device_info, driver)

    def network_error(self) -> bool:
        err_note = ["安装", "广告"]
        for item in err_note:
            match = ".*" + item + ".*"
            if self.driver.poco(textMatches=match).wait(timeout=2).exists():
                return True
        return False

    def search_app(self):
        """查找应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        while True:
            try:
                time.sleep(10)
                found = False
                textview = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1710988267887.png")
                self.driver.click(Template(textview))
                self.driver.poco_quic(_app_name, type="android.widget.EditText")
                search = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1709882347344.png")
                self.driver.click(Template(search))
                time.sleep(10)
                if not self.network_error():
                    raise ValueError("网络异常")
                matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
                for match in matches:
                    found = self.driver.poco(type="android.widget.TextView", textMatches=match).wait(timeout=5).exists()
                    if found:
                        break
                break
            except Exception as e:
                log.log_screen_store(f"{self.device_nu}-搜索应用{_app_name}异常", level=WARN)
                package = self.device_info.get("apkpure")
                self.interference()
                self.driver.stop_app(package)
                time.sleep(2)
                self.driver.start_app(package)
        if not found:
            self.app_info.success = False
            self.app_info.remark = "ApkPure应用商店未找到"
            self.driver.return_home()
            return
        self.app_info.found = True

    def install_app_online(self):
        """安装应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        matches = [f"(?i)^{_app_name}$", f"(?i)^{_app_name}(-)?.*$"]
        found = False
        install_exists = False
        try:
            for match in matches:
                found = self.driver.poco(type="android.widget.TextView", textMatches=match).exists()
                if found:
                    self.driver.poco_click(type="android.widget.TextView", textMatches=match)
                    install_exists = self.driver.poco(textMatches="^安装.*）$").exists()
                    break
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-应用程序{app_name}安装异常", level=WARN)
        if not found or not install_exists:
            self.app_info.success = False
            self.app_info.remark = "应用程序已安装或无法下载"
            log.log_screen_store(f"{self.device_nu}-应用程序{app_name}已安装，请先卸载", level=WARN)
            return
        try:
            self.driver.poco_click(textMatches="^安装.*）$")
        except Exception as e:
            self.app_info.success = False
            self.app_info.remark = "应用程序安装异常"

    def app_store(self) -> None:
        """应用商店"""
        before_third_apps = self.ct.get_third_app_list()
        self.search_app()
        if not self.app_info.success:
            return
        self.install_app_online()
        if not self.app_info.success:
            return
        self.download_status()
        if not self.app_info.success:
            return
        after_third_apps = self.ct.get_third_app_list()
        packages = list(set(after_third_apps).difference(set(before_third_apps)))
        if len(packages) == 1:
            _, self.app_info.package = packages[0].split(":")
        else:
            self.app_info.success = False
            self.app_info.remark = "无法确定APK的包名"

    def download_status(self) -> None:
        """下载状态"""
        app_name = self.app_info.name
        start_time = time.time()
        try:
            search1 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1711002211369.png")
            while True:
                flag1 = exists(Template(search1, threshold=0.9))
                if flag1:
                    self.driver.poco_click(type="android.widget.Button", text="安装")
                    search2 = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1710403510342.png")
                    while True:
                        flag2 = exists(Template(search2))
                        if flag2:
                            log.log_screen_store(
                                f"{self.device_nu}-安装应用{app_name}成功，安装耗时{int(time.time() - start_time)}秒")
                            return
                        if time.time() - start_time > config.app_install_timeout:
                            self.app_info.success = False
                            self.app_info.remark = "下载超时"
                            log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                            return
                        time.sleep(20)
                if time.time() - start_time > config.app_install_timeout:
                    self.app_info.success = False
                    self.app_info.remark = "下载超时"
                    log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                    break
                time.sleep(20)
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-下载应用{app_name}异常", level=WARN)
            self.app_info.success = False
            self.app_info.remark = "下载异常"
        finally:
            self.driver.return_home()

    def open_app_store(self) -> None:
        """应用商店抓包"""
        self.interference()
        package = self.device_info.get("apkpure")
        self.driver.start_app(package)
        time.sleep(5)
        self.interference()
        self.app_store()
        self.driver.stop_app(package)

    def interference(self):
        """点掉android系统可能的弹窗"""
        path = os.path.join(config.BASE_PATH, "resource", "screenshot", "interference", "android")
        pics = os.listdir(path)
        for item in pics:
            tmp = Template(os.path.join(path, item))
            if exists(tmp):
                self.driver.click(tmp)

    def download(self, app_info: dict):
        self.app_info.set_info(app_info)
        self.app_info.found = self.ct.exists(self.app_info.package)
        if not self.app_info.found:
            self.open_app_store()
        if self.app_info.success:
            if config.mode == 1:
                self.capture_pcap()
            elif config.mode == 0:
                self.block_analysis()
            self.lock.acquire()
            self.lock.release()
            # write_excel(self.app_info.get_info(), self.app_info.result_sheet, self.write_lock)
        try:
            if self.app_info.package:
                self.ct.uninstall(self.app_info.package)
        except Exception as e:
            log.log_screen_store(f"{self.ct.devices_no}-卸载应用异常！package_name={self.app_info.package}", level=WARN)


class MyDevice(object):
    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict):
        self.driver = set_current_driver(device_info)

    def start(self, app_info: dict):
        pass

    def check(self):
        return True


class AppStoreEN(MyDevice):
    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict):
        super().__init__(lock, write_lock, device_info)
        self.stores = {
            'google_play': Google(lock, write_lock, device_info, self.driver),
            'apkpure': ApkPure(lock, write_lock, device_info, self.driver)
        }

    @staticmethod
    def _handle_result(app_info):
        update_script_db(app_info.get_info())
        # if config.mode == 0:
        #     update_script_db(app_info.get_info())
        # else:
        #     write_excel(app_info.get_info())

    def start(self, app_info: dict):
        store_name = app_info.get(config.STORE)
        store = self.stores.get(store_name)

        if store:
            store.download(app_info)
            self._handle_result(store.app_info)
            return

        google_play = self.stores['google_play']
        google_play.download(app_info)

        if not google_play.app_info.found:
            self.stores['apkpure'].download(app_info)
            self._handle_result(self.stores['apkpure'].app_info)
        else:
            self._handle_result(google_play.app_info)

    def check(self):
        return self.driver.check_status()


class AppStoreCN(MyDevice):
    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict):
        super().__init__(lock, write_lock, device_info)
        self.stores = {
            'xiaomi': XiaoMi(lock, write_lock, device_info, self.driver),
            'yingyongbao': YingYongBao(lock, write_lock, device_info, self.driver)
        }

    @staticmethod
    def _handle_result(app_info):
        update_script_db(app_info.get_info())
        # if config.mode == 0:
        #     update_script_db(app_info.get_info())
        # else:
        #     print(app_info.get_info())

    def start(self, app_info: dict):
        store_name = app_info.get(config.STORE)
        store = self.stores.get(store_name)

        if store:
            store.download(app_info)
            self._handle_result(store.app_info)
            return

        xiaomi = self.stores['xiaomi']
        xiaomi.download(app_info)

        if not xiaomi.app_info.found:
            self.stores['yingyongbao'].download(app_info)
            self._handle_result(self.stores['yingyongbao'].app_info)
        else:
            self._handle_result(xiaomi.app_info)

    def check(self):
        return self.driver.check_status()


class AppStoreIOS(MyDevice):
    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict):
        super().__init__(lock, write_lock, device_info)
        self.appstore = AppStore(lock, write_lock, device_info, self.driver)

    def start(self, app_info: dict):
        self.appstore.download(app_info)
        # write_excel(self.appstore.app_info.get_info(), self.appstore.app_info.result_sheet, self.appstore.write_lock)
        update_script_db(self.appstore.app_info.get_info())

    def check(self):
        return self.driver.check_status()


class AppStore(Downloader):

    def __init__(self, lock: Lock, write_lock: Lock, device_info: dict, driver: Driver):
        super().__init__(lock, write_lock, device_info, driver)

    def network_error(self) -> bool:
        err_note = ["获取", "重新下载", "购买", "广告", "未找到"]
        for item in err_note:
            match = ".*" + item + ".*"
            if self.driver.poco(labelMatches=match).wait(timeout=2).exists():
                return True
        return False

    def search_app(self) -> None:
        """查找应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        while True:
            try:
                time.sleep(5)
                search_flag = False
                try_times = 3
                self.driver.poco_click(name="AppStore.tabBar.search")
                self.driver.poco_quic(_app_name, name="AppStore.searchField")
                time.sleep(10)
                if not self.network_error():
                    raise ValueError("网络异常")
                while try_times > 0:
                    matches = [f"^(?i){_app_name}$", f"^(?i){_app_name}(-)?.*$"]
                    for match in matches:
                        search_flag = self.driver.poco(nameMatches=match).wait(timeout=10).exists()
                        if search_flag:
                            break
                    if search_flag:
                        break
                    re_try = self.driver.poco(name="重试").wait(timeout=5).exists()
                    if re_try:
                        self.driver.poco(name="重试").click()
                    try_times -= 1
                break
            except Exception as e:
                log.log_screen_store(f"{self.device_nu}-搜索应用{_app_name}异常！", level=WARN)
                package = self.device_info.get("app_store")
                self.interference()
                self.driver.stop_app(package)
                time.sleep(2)
                self.driver.start_app(package)
        if not search_flag:
            self.app_info.success = False
            self.app_info.remark = "AppStore应用商店未找到"
            log.log_screen_store(f"{self.device_nu}-应用商店未找到-{_app_name}", level=WARN)
            self.driver.return_home()
            return
        self.app_info.found = True

    def install_app_online(self) -> None:
        """安装应用"""
        app_name = self.app_info.name
        _app_name = app_name.replace('_', ' ')
        matches = [f"^(?i){_app_name}$", f"^(?i){_app_name}(-)?.*$"]
        found = False
        install_exists = False
        try:
            for match in matches:
                found = self.driver.poco(nameMatches=match).wait(timeout=10).exists()
                if found:
                    self.driver.poco(nameMatches=match).click()
                    install_exists = self.driver.poco(labelMatches="(获取|重新下载)").exists()
                    break
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-应用程序{_app_name}安装异常", level=WARN)
        if not found or not install_exists:
            self.app_info.success = False
            self.app_info.remark = "应用程序已安装或无法下载"
            return
        try:
            self.driver.poco(labelMatches="(获取|重新下载)").click()
        except Exception as e:
            self.app_info.success = False
            self.app_info.remark = "应用程序安装异常"
            return
        login = self.login()
        if not login:
            self.app_info.success = False
            self.app_info.remark = "登录App Store异常"

    def login(self) -> bool:
        pwd_need = self.driver.poco(name="安装").wait(60).exists()
        if pwd_need:
            try:
                self.driver.poco_click(name="安装")
                login_exist = self.driver.poco(name="登录", type="Button").exists()
                if login_exist:
                    self.driver.poco_quic(self.device_info.get("app_store_pwd"), name="SecureTextField")
                    self.driver.poco_click(name="登录", type="Button")
                return True
            except Exception as err:
                log.log_screen_store(f"{self.device_nu}-登录App Store异常！", level=WARN)
                return False
        return True

    def app_store(self) -> None:
        """应用商店"""
        before_third_apps = self.list_app()
        self.search_app()
        if not self.app_info.success:
            return
        self.install_app_online()
        if not self.app_info.success:
            return
        self.download_status()
        if not self.app_info.success:
            return
        after_third_apps = self.list_app()
        packages = list(set(after_third_apps).difference(set(before_third_apps)))
        if len(packages) == 1:
            self.app_info.package = packages[0]
        else:
            self.app_info.success = False
            self.app_info.remark = "无法确定APK的包名"

    def download_status(self) -> None:
        """下载状态"""
        app_name = self.app_info.name
        start_time = time.time()
        try:
            search = os.path.join(config.BASE_PATH, "resource", "screenshot", "tpl1710404499923.png")
            while True:
                flag = exists(Template(search, threshold=0.9))
                if flag:
                    log.log_screen_store(
                        f"{self.device_nu}-安装应用{app_name}成功，安装耗时{int(time.time() - start_time)}秒")
                    break
                if time.time() - start_time > config.app_install_timeout:
                    log.log_screen_store(f"{self.device_nu}-安装应用{app_name}超时！", level=WARN)
                    self.app_info.success = False
                    self.app_info.remark = "下载超时"
                    break
                time.sleep(20)
            self.driver.return_home()
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-下载应用{app_name}异常", level=WARN)
            self.app_info.success = False
            self.app_info.remark = "下载异常"

    def open_app_store(self) -> None:
        """应用商店抓包"""
        self.interference()
        package = self.device_info.get("app_store")
        self.driver.start_app(package)
        time.sleep(10)
        self.interference()
        self.app_store()
        self.driver.stop_app(package)

    def interference(self):
        """点掉ios系统可能的弹窗"""
        path = os.path.join(config.BASE_PATH, "resource", "screenshot", "interference", "ios")
        pics = os.listdir(path)
        for item in pics:
            tmp = Template(os.path.join(path, item))
            if exists(tmp):
                self.driver.click(tmp)
        for item in config.ios_interference:
            if self.driver.poco(label=item).exists():
                self.driver.poco_click(label=item)

    def get_proxy(self) -> None or socket.socket:
        try:
            conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            conn.connect((config.mac_proxy_host, config.mac_proxy_port))
            return conn
        except Exception as err:
            log.log_screen_store(f"连接mac代理失败!{config.mac_proxy_host}:{config.mac_proxy_port}", level=WARN)
            return None

    def list_app(self) -> list:
        conn = self.get_proxy()
        if not conn:
            return []
        packages = []
        try:
            msg = self.device_info.get("uuid") + "||" + "all"
            conn.sendall(msg.encode('utf-8'))
            data = conn.recv(10240).decode("utf-8")
            result = data.split("||")
            for item in result:
                if item:
                    packages.append(item.split(" ")[0])
        except Exception as err:
            log.log_screen_store(f"获取远程ios设备应用包名失败！", level=WARN)
        finally:
            conn.close()
        return packages

    def uninstall(self, bundle_id: str):
        conn = self.get_proxy()
        if not conn:
            return
        try:
            msg = "uninstall" + "||" + self.device_info.get("uuid") + "||" + bundle_id
            conn.sendall(msg.encode('utf-8'))
        except Exception as err:
            log.log_screen_store(f"卸载应用{bundle_id}异常！", level=WARN)
        finally:
            conn.close()

    def download(self, app_info: dict):
        self.app_info.set_info(app_info)
        self.open_app_store()
        if self.app_info.success:
            if config.mode == 1:
                self.capture_pcap()
            elif config.mode == 0:
                self.block_analysis()
            self.lock.acquire()
            self.lock.release()
            # write_excel(self.app_info.get_info(), self.app_info.result_sheet, self.write_lock)
        try:
            if self.app_info.package:
                self.uninstall(self.app_info.package)
        except Exception as e:
            log.log_screen_store(f"{self.ct.devices_no}-卸载应用异常！package_name={self.app_info.package}", level=WARN)

    def block_analysis(self):
        """验证阻断"""
        l_class = self.app_info.l_class
        pre_search = ""
        if config.platform_type in [1, 3]:
            if self.device_info["platform"] == "Android":
                pre_search = f"brd_cn_android_case_{l_class}"
            elif self.device_info["platform"] == "IOS":
                pre_search = f"brd_cn_ios_case_{l_class}"
        else:
            if self.device_info["platform"] == "Android":
                pre_search = f"brd_en_android_case_{l_class}"
            elif self.device_info["platform"] == "IOS":
                pre_search = f"brd_en_ios_case_{l_class}"
        script_air = search_air(pre_search)
        if script_air:
            if script_air not in sys.path:
                sys.path.append(script_air)
        else:
            self.app_info.remark = "未找到录制脚本"
            self.app_info.is_blocked = "N"
            return
        try:
            _, filename = os.path.split(script_air)
            script_module = filename[:-4]
            module = importlib.import_module(script_module)
            importlib.reload(module)
            start_playback = getattr(module, "reappear_blocked")
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-脚本格式异常！-{script_air}-{str(e)}")
            self.app_info.remark = "脚本格式异常"
            self.app_info.is_blocked = "N"
            return
        # common_air = os.path.join(config.BASE_PATH, "plugins", "common.air")
        # copy_remove(common_air, script_air)
        self.device_info["router_index"] = l_class
        logdir = os.path.join(config.SCREEN_SHOT_DIR, str(l_class))
        G.BASEDIR.append(script_air)
        self.driver.scenes.clear()
        self.driver.logdir = logdir
        self.driver.create_logdir(logdir)
        self.driver.clear_dir()
        result = None
        try:
            current_time = (datetime.datetime.now() + timedelta(hours=-8)).strftime('%Y-%m-%d %H:%M:%S')
            self.driver.start_time, self.driver.end_time = current_time, current_time
            result = start_playback(dd=self.driver, l_class=l_class, dev_router=self.device_info)
        except Exception as e:
            log.log_screen_store(f"{self.device_nu}-脚本运行异常！-{script_air}-{str(e)}")
        if isinstance(result, str):
            self.app_info.is_blocked = "N"
            self.app_info.remark = result
        elif result:
            self.app_info.is_blocked = "Y"
        else:
            self.app_info.is_blocked = "N"
        self.app_info.start_time, self.app_info.end_time = self.driver.start_time, self.driver.end_time
        self.driver.logdir = ""
        G.BASEDIR.clear()
        # copy_remove(destination=script_air, copy=False)

# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   goudaowei
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   malinfor.py
@Time    :   2023/9/10 11:17
"""
import cv2
import numpy as np
import matplotlib.pyplot as plt


def match():
    logo = r'D:\code\appautoanalysis\resource\login\6.png'
    login = r"D:\code\appautoanalysis\resource\screenshot\16600-2023-11-15-185650.png"
    pic_login = cv2.imread(login)
    gray_scale_login = cv2.cvtColor(pic_login, cv2.IMREAD_GRAYSCALE)
    pic_logo = cv2.imread(logo)
    gray_scale_logo = cv2.cvtColor(pic_logo, cv2.IMREAD_GRAYSCALE)
    # plt.figure(figsize=(11, 7))
    # plt.imshow(pic_login),plt.imshow(pic_logo)
    # 特征提取
    orb = cv2.ORB_create()
    kp1, des1 = orb.detectAndCompute(gray_scale_login, None)
    kp2, des2 = orb.detectAndCompute(gray_scale_logo, None)

    # 可视化特征点
    keypoint_1 = cv2.drawKeypoints(image=pic_login, outImage=pic_login, keypoints=kp1,
                                   flags=4, color=(0, 255, 0))
    keypoint_2 = cv2.drawKeypoints(image=pic_logo, outImage=pic_logo, keypoints=kp2,
                                   flags=4, color=(0, 255, 0))
    # plt.figure(figsize=(11, 7))
    # plt.imshow(keypoint_2)
    # 特征匹配
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    matches = bf.match(des1, des2)
    matches = sorted(matches, key=lambda x: x.distance)
    # 特征匹配结果的可视化
    match_img = cv2.drawMatches(pic_login, kp1, pic_logo, kp2, matches[:30], pic_logo, flags=2)
    plt.figure(figsize=(15, 8))
    plt.imshow(match_img)

    plt.show()
    print("success")
    pass

def cv_show(img, name):
    cv2.imshow(name, img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()


def find_contour():
    login = r"D:\code\appautoanalysis\resource\screenshot\16984.png"
    img = cv2.imread(login)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    ret, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
    contours, hierachy = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_NONE)
    draw_img = img.copy()
    ret = cv2.drawContours(draw_img, contours, -1, (0, 0, 255), 2)
    cv_show(ret, 'ret')





if __name__ == "__main__":
    find_contour()
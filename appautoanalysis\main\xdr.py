# -*- encoding: utf-8 -*-
"""
@License :   (C) Copyright 2021-2021, broadtech.com.cn
<AUTHOR>   go<PERSON>ow<PERSON>
@Contact :   <EMAIL>
@Software:   PyCharm
@File    :   xdr.py
@Time    :   2023/7/25 19:11
"""
import os
import csv
import subprocess
import datetime

source = r"D:\data\拨测\回溯\xdr\output.csv"
header = ["ruleID", "createTime", "endTime", "content", "xdr"]
result_dir = "/tmp/malware_xdr.csv"


def _encoding(info: bytes, err: bytes) -> (str, str):
    codes = ["utf-8", "gbk"]
    for _encode in codes:
        try:
            return str(info, encoding=_encode).replace('\n', ''), str(err, encoding=_encode)
        except Exception:
            pass
    return str(info), str(err)


def count():
    data = []
    result_excel = f"xdr-{datetime.datetime.now().strftime('%Y-%m-%d-%H%M%S')}.csv"
    with open(source, 'r', encoding="UTF-8") as csvfile:
        reader = csv.reader(csvfile)
        for index, row in enumerate(reader):
            if index == 0:
                continue
            row[0] = row[0] + '\t'
            create_time = row[1].split(" ")[0]
            cmd = f"cat /data/ngs-agent-go/xdr/cm_file_monitor* | grep {row[0]} | grep {create_time} | wc -l"
            result = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            b_out, b_err = result.communicate(timeout=300)
            out, err = _encoding(b_out, b_err)
            row.append(out)
            data.append(row)
    csv_result = os.path.join(result_dir, result_excel)
    if not os.path.exists(csv_result):
        with open(csv_result, 'w', encoding="UTF-8", newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
        with open(csv_result, 'a+', encoding="UTF-8", newline='') as f:
            writer = csv.writer(f)
            for row in data:
                writer.writerow(row)


if __name__ == "__main__":
    count()



